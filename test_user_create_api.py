#!/usr/bin/env python3
"""
测试用户新增API功能
"""
import requests
import json

def test_user_create_api():
    """测试用户新增API"""
    
    # 1. 先登录获取token
    print("🔐 正在登录...")
    login_response = requests.post('http://localhost:8000/api/v1/auth/login/',
        json={
            'userName': 'mpr_admin',
            'password': 'admin123'
        },
        headers={'Content-Type': 'application/json', 'X-Region-Code': 'MPR'}
    )
    
    print(f'登录状态码: {login_response.status_code}')
    if login_response.status_code == 200:
        data = login_response.json()
        print('✅ 登录成功!')
        response_data = data.get('data', {})
        token = response_data.get('token', '')
        print(f'Token: {token[:50]}...')
        user_info = response_data.get('userInfo', {})
        print(f'用户: {user_info.get("username")} ({user_info.get("role")})')
        print(f'区域: {user_info.get("regionName", "无")}')
        
        # 2. 测试创建用户
        print('\n👤 测试创建用户...')
        create_response = requests.post('http://localhost:8000/api/v1/user/list/',
            json={
                'username': 'test_user_001',
                'email': '<EMAIL>',
                'phone': '13800138001',
                'role': 'MEMBER',
                'password': 'test123456'
            },
            headers={
                'Authorization': token,
                'Content-Type': 'application/json',
                'X-Region-Code': 'MPR'
            }
        )
        
        print(f'创建用户状态码: {create_response.status_code}')
        print(f'创建用户响应: {create_response.text}')
        
        if create_response.status_code == 201:
            create_data = create_response.json()
            print('✅ 用户创建成功!')
            print(f'用户信息: {json.dumps(create_data.get("data", {}), indent=2, ensure_ascii=False)}')
        else:
            print('❌ 用户创建失败')
            
        # 3. 测试获取用户列表，验证新用户是否存在
        print('\n📋 验证用户列表...')
        list_response = requests.get('http://localhost:8000/api/v1/user/list/?current=1&size=20',
            headers={
                'Authorization': token,
                'X-Region-Code': 'MPR'
            }
        )
        
        print(f'获取用户列表状态码: {list_response.status_code}')
        if list_response.status_code == 200:
            list_data = list_response.json()
            users = list_data.get('data', {}).get('records', [])
            print(f'用户总数: {len(users)}')
            
            # 查找新创建的用户
            test_user = None
            for user in users:
                if user.get('userName') == 'test_user_001':
                    test_user = user
                    break
            
            if test_user:
                print('✅ 新用户已出现在列表中!')
                print(f'用户详情: {json.dumps(test_user, indent=2, ensure_ascii=False)}')
            else:
                print('❌ 新用户未在列表中找到')
                print('现有用户列表:')
                for user in users:
                    print(f'  - {user.get("userName")} ({user.get("userEmail")})')
        else:
            print('❌ 获取用户列表失败')
            
    else:
        print('❌ 登录失败')
        print(f'响应: {login_response.text}')

if __name__ == '__main__':
    test_user_create_api()
