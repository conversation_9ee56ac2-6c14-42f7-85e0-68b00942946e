<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogType === 'add' ? '添加用户' : '编辑用户'"
    width="30%"
    align-center
  >
    <ElForm ref="formRef" :model="formData" :rules="rules" label-width="80px">
      <ElFormItem label="用户名" prop="username">
        <ElInput v-model="formData.username" />
      </ElFormItem>
      <ElFormItem label="邮箱" prop="email">
        <ElInput v-model="formData.email" />
      </ElFormItem>
      <ElFormItem label="手机号" prop="phone">
        <ElInput v-model="formData.phone" />
      </ElFormItem>
      <ElFormItem label="角色" prop="role">
        <ElSelect v-model="formData.role" @change="handleRoleChange">
          <ElOption
            v-for="role in roleOptions"
            :key="role.value"
            :value="role.value"
            :label="role.label"
          />
        </ElSelect>
      </ElFormItem>
      <ElFormItem v-if="formData.role === 'MEMBER'" label="负责助理" prop="managedById">
        <ElSelect v-model="formData.managedById" placeholder="请选择负责助理（可选）" clearable>
          <ElOption
            v-for="assistant in assistantList"
            :key="assistant.id"
            :value="assistant.id"
            :label="`${assistant.username} (${assistant.email})`"
          />
        </ElSelect>
      </ElFormItem>

      <!-- 助理负责会员选择 -->
      <ElFormItem v-if="formData.role === 'MEMBER_ASSISTANT'" label="负责会员" prop="responsibleMemberIds">
        <ElSelect
          v-model="formData.responsibleMemberIds"
          multiple
          placeholder="请选择负责的会员（可选）"
          clearable
          style="width: 100%"
        >
          <ElOption
            v-for="member in memberList"
            :key="member.id"
            :value="member.id"
            :label="`${member.username} (${member.email})`"
          />
        </ElSelect>
      </ElFormItem>

      <!-- 权限选择 -->
      <ElFormItem v-if="shouldShowPermissions" label="用户权限" prop="permissionIds">
        <div style="max-height: 200px; overflow-y: auto; border: 1px solid #dcdfe6; border-radius: 4px; padding: 10px;">
          <div v-for="(permissions, category) in permissionCategories" :key="category" style="margin-bottom: 15px;">
            <div style="font-weight: bold; margin-bottom: 8px; color: #409eff;">{{ category }}</div>
            <ElCheckboxGroup v-model="formData.permissionIds">
              <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px;">
                <ElCheckbox
                  v-for="permission in permissions"
                  :key="permission.id"
                  :value="permission.id"
                  style="margin: 0;"
                >
                  {{ permission.name }}
                </ElCheckbox>
              </div>
            </ElCheckboxGroup>
          </div>
        </div>
      </ElFormItem>

      <ElFormItem v-if="dialogType === 'add'" label="初始密码" prop="password">
        <ElInput v-model="formData.password" type="password" placeholder="留空则使用默认密码" />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="dialogVisible = false">取消</ElButton>
        <ElButton type="primary" @click="handleSubmit">提交</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import type { FormInstance, FormRules } from 'element-plus'
  import { ElMessage, ElCheckbox, ElCheckboxGroup } from 'element-plus'
  import { UserService } from '@/api/usersApi'

  interface Props {
    visible: boolean
    type: string
    userData?: any
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void
    (e: 'submit'): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 角色选项
  const roleOptions = ref([
    { value: 'MEMBER', label: '普通会员' },
    { value: 'MEMBER_ASSISTANT', label: '会员助理' },
    { value: 'WAREHOUSE_MANAGER', label: '库管' },
    { value: 'REGION_ADMIN', label: '区域管理员' },
    { value: 'SUPER_ADMIN', label: '超级管理员' }
  ])

  // 对话框显示控制
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })

  const dialogType = computed(() => props.type)

  // 是否显示权限选择
  const shouldShowPermissions = computed(() => {
    // 超级管理员不需要选择权限（拥有所有权限）
    return formData.role !== 'SUPER_ADMIN'
  })

  // 表单实例
  const formRef = ref<FormInstance>()

  // 表单数据
  const formData = reactive({
    username: '',
    email: '',
    phone: '',
    role: 'MEMBER',
    password: '',
    managedById: null as number | null,
    permissionIds: [] as number[],
    responsibleMemberIds: [] as number[]
  })

  // 助理列表
  const assistantList = ref<Api.Common.AssistantInfo[]>([])

  // 会员列表
  const memberList = ref<Api.Common.MemberInfo[]>([])

  // 权限列表
  const permissionList = ref<Api.Common.PermissionInfo[]>([])
  const permissionCategories = ref<Record<string, Api.Common.PermissionInfo[]>>({})

  // 获取助理列表
  const getAssistantList = async () => {
    try {
      const result = await UserService.getAssistantList()
      assistantList.value = result
    } catch (error) {
      console.error('获取助理列表失败:', error)
    }
  }

  // 获取会员列表
  const getMemberList = async () => {
    try {
      const result = await UserService.getMemberList()
      memberList.value = result
    } catch (error) {
      console.error('获取会员列表失败:', error)
    }
  }

  // 获取权限列表
  const getPermissionList = async () => {
    try {
      const result = await UserService.getPermissionList()
      permissionList.value = result.permissions
      permissionCategories.value = result.categories
    } catch (error) {
      console.error('获取权限列表失败:', error)
    }
  }

  // 角色变化处理
  const handleRoleChange = (role: string) => {
    // 清空相关字段
    formData.managedById = null
    formData.responsibleMemberIds = []
    formData.permissionIds = []

    if (role === 'MEMBER') {
      // 当选择会员角色时，获取助理列表
      getAssistantList()
    } else if (role === 'MEMBER_ASSISTANT') {
      // 当选择助理角色时，获取会员列表
      getMemberList()
    }

    // 获取权限列表（除了超级管理员）
    if (role !== 'SUPER_ADMIN') {
      getPermissionList()
    }
  }

  // 表单验证规则
  const rules: FormRules = {
    username: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    phone: [
      { required: false, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
    ],
    role: [{ required: true, message: '请选择角色', trigger: 'blur' }]
  }

  // 初始化表单数据
  const initFormData = async () => {
    const isEdit = props.type === 'edit' && props.userData
    const row = props.userData

    Object.assign(formData, {
      username: isEdit ? row.userName || '' : '',
      email: isEdit ? row.userEmail || '' : '',
      phone: isEdit ? row.userPhone || '' : '',
      role: isEdit ? row.role || 'MEMBER' : 'MEMBER',
      password: '',
      managedById: null,
      permissionIds: [],
      responsibleMemberIds: []
    })

    // 编辑模式下加载用户详情
    if (isEdit && row.id) {
      try {
        const userDetail = await UserService.getUserDetail(row.id)
        const userData = userDetail.data

        // 更新表单数据
        Object.assign(formData, {
          username: userData.username,
          email: userData.email,
          phone: userData.phone,
          role: userData.role,
          managedById: userData.managedBy?.id || null,
          permissionIds: userData.permissions.map(p => p.id),
          responsibleMemberIds: userData.responsibleMembers.map(m => m.id)
        })
      } catch (error) {
        console.error('加载用户详情失败:', error)
        ElMessage.error('加载用户详情失败')
      }
    }

    // 根据角色初始化相关数据
    if (formData.role === 'MEMBER') {
      getAssistantList()
    } else if (formData.role === 'MEMBER_ASSISTANT') {
      getMemberList()
    }

    // 获取权限列表（除了超级管理员）
    if (formData.role !== 'SUPER_ADMIN') {
      getPermissionList()
    }
  }

  // 统一监听对话框状态变化
  watch(
    () => [props.visible, props.type, props.userData],
    ([visible]) => {
      if (visible) {
        initFormData()
        nextTick(() => {
          formRef.value?.clearValidate()
        })
      }
    },
    { immediate: true }
  )

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      const valid = await formRef.value.validate()
      if (!valid) return

      if (dialogType.value === 'add') {
        // 新增用户
        const params: Api.User.CreateUserParams = {
          username: formData.username,
          email: formData.email,
          phone: formData.phone,
          role: formData.role,
          password: formData.password || undefined,
          managed_by_id: formData.role === 'MEMBER' ? formData.managedById : undefined,
          permission_ids: formData.role !== 'SUPER_ADMIN' ? formData.permissionIds : undefined,
          responsible_member_ids: formData.role === 'MEMBER_ASSISTANT' ? formData.responsibleMemberIds : undefined
        }

        const result = await UserService.createUser(params)
        ElMessage.success(`用户创建成功！默认密码：${result.defaultPassword}`)
      } else {
        // 更新用户
        const params: Api.User.UpdateUserParams = {
          username: formData.username,
          email: formData.email,
          phone: formData.phone,
          role: formData.role,
          permission_ids: formData.role !== 'SUPER_ADMIN' ? formData.permissionIds : undefined,
          responsible_member_ids: formData.role === 'MEMBER_ASSISTANT' ? formData.responsibleMemberIds : undefined
        }

        const result = await UserService.updateUser(props.userData.id, params)
        ElMessage.success('用户更新成功')
      }

      dialogVisible.value = false
      emit('submit')
    } catch (error: any) {
      console.error('提交失败:', error)
      ElMessage.error(error.message || '操作失败，请重试')
    }
  }
</script>
