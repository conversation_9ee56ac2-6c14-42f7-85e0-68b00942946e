# core/permissions.py
"""
用户级权限控制模块
提供装饰器、混入类和权限检查函数
"""

from functools import wraps
from django.http import JsonResponse
from django.core.exceptions import PermissionDenied
from rest_framework import status
from rest_framework.response import Response
from rest_framework.permissions import BasePermission
from .middleware import get_current_user


def user_permission_required(permission_check=None):
    """
    用户权限装饰器
    用于视图函数的权限检查
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            user = get_current_user()
            
            if not user or not user.is_authenticated:
                return JsonResponse({
                    'code': 401,
                    'msg': '用户未认证',
                    'data': None
                }, status=401)
            
            # 如果提供了自定义权限检查函数
            if permission_check and not permission_check(user, request, *args, **kwargs):
                return JsonResponse({
                    'code': 403,
                    'msg': '权限不足',
                    'data': None
                }, status=403)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


class UserPermissionMixin:
    """
    用户权限混入类
    为类视图提供用户级权限控制
    """
    
    def check_user_permission(self, request, *args, **kwargs):
        """
        检查用户权限，子类可以重写此方法
        """
        return True
    
    def dispatch(self, request, *args, **kwargs):
        """
        重写dispatch方法，添加权限检查
        """
        user = get_current_user()
        
        if not user or not user.is_authenticated:
            return Response({
                'code': 401,
                'msg': '用户未认证',
                'data': None
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        if not self.check_user_permission(request, *args, **kwargs):
            return Response({
                'code': 403,
                'msg': '权限不足',
                'data': None
            }, status=status.HTTP_403_FORBIDDEN)
        
        return super().dispatch(request, *args, **kwargs)


class IsOwnerOrAdmin(BasePermission):
    """
    DRF权限类：只有数据所有者或管理员可以访问
    """
    
    def has_permission(self, request, view):
        """检查用户是否有基本权限"""
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        """检查用户是否有对象权限"""
        user = request.user
        
        # 超级管理员和区域管理员有所有权限
        if user.role in ['SUPER_ADMIN', 'REGION_ADMIN']:
            return True
        
        # 检查是否是数据所有者
        if hasattr(obj, 'created_by_id'):
            return obj.created_by_id == user.id
        
        return False


class IsRegionAdmin(BasePermission):
    """
    DRF权限类：只有区域管理员及以上可以访问
    """
    
    def has_permission(self, request, view):
        user = request.user
        return (user and user.is_authenticated and 
                user.role in ['SUPER_ADMIN', 'REGION_ADMIN'])


class IsWarehouseManager(BasePermission):
    """
    DRF权限类：库管权限检查
    """
    
    def has_permission(self, request, view):
        user = request.user
        return (user and user.is_authenticated and 
                user.role in ['SUPER_ADMIN', 'REGION_ADMIN', 'WAREHOUSE_MANAGER'])


def check_data_access_permission(user, obj):
    """
    检查用户是否有访问指定数据的权限
    """
    if not user or not user.is_authenticated:
        return False
    
    # 超级管理员可以访问所有数据
    if user.role == 'SUPER_ADMIN':
        return True
    
    # 区域管理员可以访问区域内所有数据
    if user.role == 'REGION_ADMIN':
        return True
    
    # 检查是否是数据所有者
    if hasattr(obj, 'created_by_id'):
        if obj.created_by_id == user.id:
            return True
    
    # 会员助理的特殊权限逻辑（可以扩展）
    if user.role == 'MEMBER_ASSISTANT':
        # TODO: 实现会员助理的授权逻辑
        # 例如：检查是否被授权访问某些数据
        return hasattr(obj, 'created_by_id') and obj.created_by_id == user.id
    
    # 库管可以访问库存相关数据
    if user.role == 'WAREHOUSE_MANAGER':
        # TODO: 根据具体业务需求实现库管权限
        return True
    
    return False


def check_data_modify_permission(user, obj):
    """
    检查用户是否有修改指定数据的权限
    """
    if not user or not user.is_authenticated:
        return False
    
    # 超级管理员和区域管理员可以修改所有数据
    if user.role in ['SUPER_ADMIN', 'REGION_ADMIN']:
        return True
    
    # 检查是否是数据所有者
    if hasattr(obj, 'created_by_id'):
        if obj.created_by_id == user.id:
            return True
    
    # 库管可以修改库存相关数据
    if user.role == 'WAREHOUSE_MANAGER':
        return True
    
    return False


def filter_queryset_by_user_permission(queryset, user):
    """
    根据用户权限过滤查询集
    这个函数可以在视图中手动调用，用于额外的权限控制
    """
    if not user or not user.is_authenticated:
        return queryset.none()
    
    # 超级管理员和区域管理员可以看到所有数据
    if user.role in ['SUPER_ADMIN', 'REGION_ADMIN']:
        return queryset
    
    # 普通用户只能看到自己创建的数据
    if user.role in ['MEMBER', 'MEMBER_ASSISTANT']:
        return queryset.filter(created_by_id=user.id)
    
    # 库管可以看到所有数据
    if user.role == 'WAREHOUSE_MANAGER':
        return queryset
    
    # 默认只能看到自己创建的数据
    return queryset.filter(created_by_id=user.id)
