# business/admin.py
from django.contrib import admin
from .models import Customer, Product, Order, OrderItem, Inventory


@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = ['customer_code', 'name', 'contact_person', 'phone', 'created_at']
    list_filter = ['created_at', 'is_active']
    search_fields = ['customer_code', 'name', 'contact_person']
    ordering = ['-created_at']


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ['product_code', 'name', 'category', 'price', 'unit', 'created_at']
    list_filter = ['category', 'created_at', 'is_active']
    search_fields = ['product_code', 'name', 'category']
    ordering = ['-created_at']


class OrderItemInline(admin.TabularInline):
    model = OrderItem
    extra = 1


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ['order_number', 'customer', 'status', 'total_amount', 'order_date']
    list_filter = ['status', 'order_date', 'created_at']
    search_fields = ['order_number', 'customer__name']
    ordering = ['-order_date']
    inlines = [OrderItemInline]


@admin.register(Inventory)
class InventoryAdmin(admin.ModelAdmin):
    list_display = ['product', 'warehouse_location', 'quantity_on_hand', 'quantity_available', 'last_updated']
    list_filter = ['warehouse_location', 'last_updated']
    search_fields = ['product__name', 'product__product_code']
    ordering = ['-last_updated']
