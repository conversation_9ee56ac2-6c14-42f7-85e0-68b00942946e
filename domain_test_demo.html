<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多域名支持测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .test-section h2 {
            color: #1976d2;
            margin-bottom: 15px;
            font-size: 20px;
        }
        .domain-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .domain-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4caf50;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .domain-card h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }
        .domain-info {
            font-size: 14px;
            color: #666;
            margin: 5px 0;
        }
        .test-button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
            margin-right: 10px;
        }
        .test-button:hover {
            background: #1976d2;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }
        .error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
        }
        .region-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .region-mpr { background: #2196f3; color: white; }
        .region-rl { background: #4caf50; color: white; }
        .region-eo { background: #ff9800; color: white; }
        .region-zz { background: #9c27b0; color: white; }
        .region-wh { background: #f44336; color: white; }
        .region-global { background: #607d8b; color: white; }
        
        .current-domain {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .current-domain h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 多域名支持测试</h1>
        
        <div class="current-domain">
            <h3>当前访问域名</h3>
            <div id="currentDomain">正在检测...</div>
        </div>
        
        <div class="test-section">
            <h2>📋 支持的域名列表</h2>
            <div class="domain-grid">
                <div class="domain-card">
                    <h3>MPR区域 <span class="region-badge region-mpr">MPR</span></h3>
                    <div class="domain-info">域名: mpr.dawn-erp.com</div>
                    <div class="domain-info">区域: MPR（默认区域）</div>
                    <div class="domain-info">权限: 区域管理</div>
                    <button class="test-button" onclick="testDomain('mpr.dawn-erp.com')">测试域名</button>
                    <button class="test-button" onclick="simulateDomain('mpr.dawn-erp.com')">模拟访问</button>
                </div>
                
                <div class="domain-card">
                    <h3>RL区域 <span class="region-badge region-rl">RL</span></h3>
                    <div class="domain-info">域名: rl.dawn-erp.com</div>
                    <div class="domain-info">区域: RL区域</div>
                    <div class="domain-info">权限: 区域管理</div>
                    <button class="test-button" onclick="testDomain('rl.dawn-erp.com')">测试域名</button>
                    <button class="test-button" onclick="simulateDomain('rl.dawn-erp.com')">模拟访问</button>
                </div>
                
                <div class="domain-card">
                    <h3>EO区域 <span class="region-badge region-eo">EO</span></h3>
                    <div class="domain-info">域名: eo.dawn-erp.com</div>
                    <div class="domain-info">区域: EO区域</div>
                    <div class="domain-info">权限: 区域管理</div>
                    <button class="test-button" onclick="testDomain('eo.dawn-erp.com')">测试域名</button>
                    <button class="test-button" onclick="simulateDomain('eo.dawn-erp.com')">模拟访问</button>
                </div>
                
                <div class="domain-card">
                    <h3>郑州区域 <span class="region-badge region-zz">ZZ</span></h3>
                    <div class="domain-info">域名: zz.dawn-erp.com</div>
                    <div class="domain-info">区域: ZZ区域（郑州）</div>
                    <div class="domain-info">权限: 区域管理</div>
                    <button class="test-button" onclick="testDomain('zz.dawn-erp.com')">测试域名</button>
                    <button class="test-button" onclick="simulateDomain('zz.dawn-erp.com')">模拟访问</button>
                </div>
                
                <div class="domain-card">
                    <h3>武汉区域 <span class="region-badge region-wh">WH</span></h3>
                    <div class="domain-info">域名: wh.dawn-erp.com</div>
                    <div class="domain-info">区域: WH区域（武汉）</div>
                    <div class="domain-info">权限: 区域管理</div>
                    <button class="test-button" onclick="testDomain('wh.dawn-erp.com')">测试域名</button>
                    <button class="test-button" onclick="simulateDomain('wh.dawn-erp.com')">模拟访问</button>
                </div>
                
                <div class="domain-card">
                    <h3>全局管理 <span class="region-badge region-global">GLOBAL</span></h3>
                    <div class="domain-info">域名: localhost:8000</div>
                    <div class="domain-info">区域: 全局（超级管理员）</div>
                    <div class="domain-info">权限: 全局管理</div>
                    <button class="test-button" onclick="testDomain('localhost:8000')">测试域名</button>
                    <button class="test-button" onclick="getCurrentDomainInfo()">当前域名信息</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔍 域名识别测试</h2>
            <button class="test-button" onclick="testAllDomains()">批量测试所有域名</button>
            <button class="test-button" onclick="getCurrentDomainInfo()">获取当前域名信息</button>
            <button class="test-button" onclick="clearResults()">清空结果</button>
        </div>

        <div id="testResults" class="result" style="display: none;"></div>
    </div>

    <script>
        // 页面加载时显示当前域名
        window.onload = function() {
            const currentDomainDiv = document.getElementById('currentDomain');
            currentDomainDiv.innerHTML = `
                <strong>域名:</strong> ${window.location.host}<br>
                <strong>协议:</strong> ${window.location.protocol}<br>
                <strong>完整URL:</strong> ${window.location.href}
            `;
        };

        async function getCurrentDomainInfo() {
            const resultDiv = document.getElementById('testResults');
            resultDiv.style.display = 'block';
            
            try {
                const response = await fetch('http://127.0.0.1:8000/api/v1/system/domain-info/', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 当前域名信息获取成功！

请求信息:
- 主机: ${data.data.request_info.host}
- 路径: ${data.data.request_info.path}
- 方法: ${data.data.request_info.method}

域名信息:
- 域名: ${data.data.domain_info?.host || '未识别'}
- 区域代码: ${data.data.domain_info?.region_code || '无'}
- 是否支持: ${data.data.domain_info?.is_supported ? '是' : '否'}
- 访问类型: ${data.data.domain_info?.access_type || '未知'}

区域信息:
- 当前区域: ${data.data.region_info?.current_region?.name || '无'}
- 区域代码: ${data.data.region_info?.current_region?.code || '无'}
- 访问类型: ${data.data.region_info?.access_type || '未知'}

完整响应:
${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 获取域名信息失败: ${data.msg || '未知错误'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误: ${error.message}`;
            }
        }

        async function testDomain(domain) {
            const resultDiv = document.getElementById('testResults');
            resultDiv.style.display = 'block';
            
            resultDiv.className = 'result success';
            resultDiv.textContent = `🔍 测试域名: ${domain}

注意: 由于当前在 ${window.location.host} 访问，无法直接测试其他域名。
要完整测试多域名功能，需要：

1. 配置本地hosts文件:
   127.0.0.1 mpr.dawn-erp.com
   127.0.0.1 rl.dawn-erp.com
   127.0.0.1 eo.dawn-erp.com
   127.0.0.1 zz.dawn-erp.com
   127.0.0.1 wh.dawn-erp.com

2. 通过不同域名访问:
   http://mpr.dawn-erp.com:8000
   http://rl.dawn-erp.com:8000
   等等...

3. 或者使用请求头模拟:`;
            
            // 模拟使用请求头测试
            await simulateDomainWithHeader(domain);
        }

        async function simulateDomainWithHeader(domain) {
            const regionCode = extractRegionFromDomain(domain);
            if (!regionCode) return;
            
            try {
                const response = await fetch('http://127.0.0.1:8000/api/v1/system/domain-info/', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Region-Code': regionCode
                    }
                });

                const data = await response.json();
                const resultDiv = document.getElementById('testResults');
                
                if (response.ok) {
                    resultDiv.textContent += `

✅ 使用请求头 X-Region-Code: ${regionCode} 模拟成功！
- 识别区域: ${data.data.region_info?.current_region?.name || '无'}
- 区域代码: ${data.data.region_info?.current_region?.code || '无'}`;
                }
            } catch (error) {
                console.error('模拟测试失败:', error);
            }
        }

        function extractRegionFromDomain(domain) {
            const mapping = {
                'mpr.': 'MPR',
                'rl.': 'RL',
                'eo.': 'EO',
                'zz.': 'ZZ',
                'wh.': 'WH'
            };
            
            for (const [prefix, code] of Object.entries(mapping)) {
                if (domain.startsWith(prefix)) {
                    return code;
                }
            }
            return null;
        }

        async function simulateDomain(domain) {
            const regionCode = extractRegionFromDomain(domain);
            if (regionCode) {
                await simulateDomainWithHeader(domain);
            } else {
                await getCurrentDomainInfo();
            }
        }

        async function testAllDomains() {
            const domains = [
                'mpr.dawn-erp.com',
                'rl.dawn-erp.com', 
                'eo.dawn-erp.com',
                'zz.dawn-erp.com',
                'wh.dawn-erp.com'
            ];

            const resultDiv = document.getElementById('testResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result success';
            resultDiv.textContent = '🚀 开始批量测试所有域名...\n\n';

            for (const domain of domains) {
                const regionCode = extractRegionFromDomain(domain);
                if (regionCode) {
                    try {
                        const response = await fetch('http://127.0.0.1:8000/api/v1/system/domain-info/', {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Region-Code': regionCode
                            }
                        });

                        const data = await response.json();
                        
                        if (response.ok) {
                            resultDiv.textContent += `✅ ${domain} (${regionCode})
   - 识别区域: ${data.data.region_info?.current_region?.name || '无'}
   - 访问类型: ${data.data.region_info?.access_type || '未知'}
   - 状态: 正常 ✓

`;
                        } else {
                            resultDiv.textContent += `❌ ${domain}: 测试失败

`;
                        }
                    } catch (error) {
                        resultDiv.textContent += `❌ ${domain}: 网络错误 - ${error.message}

`;
                    }
                    
                    await new Promise(resolve => setTimeout(resolve, 300));
                }
            }

            resultDiv.textContent += '\n🎉 批量测试完成！多域名支持功能运行正常。';
        }

        function clearResults() {
            const resultDiv = document.getElementById('testResults');
            resultDiv.style.display = 'none';
            resultDiv.textContent = '';
        }
    </script>
</body>
</html>
