from rest_framework import serializers
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from .models import CustomUser
from .middleware import get_current_region

class UserRegistrationSerializer(serializers.ModelSerializer):
    """用户注册序列化器 - 支持区域感知注册"""
    
    password = serializers.CharField(
        write_only=True, 
        min_length=8, 
        style={'input_type': 'password'}
    )
    password2 = serializers.CharField(
        write_only=True, 
        style={'input_type': 'password'}
    )
    
    class Meta:
        model = CustomUser
        fields = ('username', 'email', 'password', 'password2', 'phone')
        extra_kwargs = {
            'email': {'required': True},
            'phone': {'required': False}
        }

    def validate(self, attrs):
        """验证密码一致性和区域要求"""
        
        # 1. 验证两次密码是否一致
        if attrs['password'] != attrs['password2']:
            raise serializers.ValidationError({
                'password2': '两次输入的密码不一致'
            })
        
        # 2. 验证密码强度
        try:
            validate_password(attrs['password'])
        except ValidationError as e:
            raise serializers.ValidationError({
                'password': list(e.messages)
            })
        
        # 3. 获取当前区域，确保区域感知
        region = get_current_region()
        if not region:
            raise serializers.ValidationError({
                'region': '注册请求必须包含有效的区域代码(X-Region-Code请求头)'
            })
        
        # 将区域信息保存到验证后的数据中，供create方法使用
        attrs['region'] = region
        
        return attrs

    def validate_username(self, value):
        """验证用户名在当前区域内是否唯一"""
        region = get_current_region()
        if region:
            # 使用区域感知管理器检查用户名是否已存在
            if CustomUser.objects.filter(username=value).exists():
                raise serializers.ValidationError(
                    f'用户名 "{value}" 在当前区域已存在'
                )
        return value

    def validate_email(self, value):
        """验证邮箱在当前区域内是否唯一"""
        region = get_current_region()
        if region:
            # 使用区域感知管理器检查邮箱是否已存在
            if CustomUser.objects.filter(email=value).exists():
                raise serializers.ValidationError(
                    f'邮箱 "{value}" 在当前区域已存在'
                )
        return value

    def create(self, validated_data):
        """创建新用户，自动关联区域和默认角色"""
        
        # 移除确认密码字段
        validated_data.pop('password2', None)
        
        # 获取区域信息
        region = validated_data.pop('region')
        
        # 提取密码
        password = validated_data.pop('password')
        
        # 创建用户实例
        user = CustomUser(
            **validated_data,
            region=region,
            role=CustomUser.Role.MEMBER  # 默认为普通会员
        )
        
        # 设置加密密码
        user.set_password(password)
        user.save()
        
        return user

    def to_representation(self, instance):
        """自定义返回数据，不包含敏感信息"""
        data = super().to_representation(instance)
        # 移除密码字段
        data.pop('password', None)
        data.pop('password2', None)
        
        # 添加一些有用的信息
        data.update({
            'id': instance.id,
            'region': instance.region.name if instance.region else None,
            'region_code': instance.region.code if instance.region else None,
            'role': instance.get_role_display(),
            'date_joined': instance.date_joined.isoformat() if instance.date_joined else None
        })
        
        return data 