<template>
  <ElDialog
    v-model="dialogVisible"
    title="权限管理"
    width="60%"
    align-center
  >
    <div class="permission-management">
      <!-- 用户信息 -->
      <ElCard class="user-info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>用户信息</span>
          </div>
        </template>
        <div class="user-info">
          <ElDescriptions :column="3" border>
            <ElDescriptionsItem label="用户名">{{ userInfo.username }}</ElDescriptionsItem>
            <ElDescriptionsItem label="邮箱">{{ userInfo.email }}</ElDescriptionsItem>
            <ElDescriptionsItem label="角色">{{ userInfo.roleDisplay }}</ElDescriptionsItem>
            <ElDescriptionsItem label="区域">{{ userInfo.regionName || '未分配' }}</ElDescriptionsItem>
            <ElDescriptionsItem label="负责助理" v-if="userInfo.role === 'MEMBER'">
              {{ userInfo.managedBy?.username || '未分配' }}
            </ElDescriptionsItem>
            <ElDescriptionsItem label="负责会员数" v-if="userInfo.role === 'MEMBER_ASSISTANT'">
              {{ userInfo.responsibleMembers.length }} 个
            </ElDescriptionsItem>
          </ElDescriptions>
        </div>
      </ElCard>

      <!-- 权限管理 -->
      <ElCard class="permission-card" shadow="never" style="margin-top: 20px;">
        <template #header>
          <div class="card-header">
            <span>权限配置</span>
            <div class="header-actions">
              <ElButton type="primary" size="small" @click="savePermissions" :loading="saving">
                保存权限
              </ElButton>
              <ElButton size="small" @click="resetPermissions">
                重置
              </ElButton>
            </div>
          </div>
        </template>

        <div v-if="userInfo.role === 'SUPER_ADMIN'" class="super-admin-notice">
          <ElAlert
            title="超级管理员拥有所有权限"
            type="info"
            :closable="false"
            show-icon
          />
        </div>

        <div v-else class="permission-content">
          <!-- 调试信息 -->
          <div class="debug-info" style="margin-bottom: 16px; padding: 12px; background: #f5f7fa; border-radius: 4px;">
            <p><strong>调试信息：</strong></p>
            <p>用户ID: {{ props.userId }}</p>
            <p>用户名: {{ userInfo.username }}</p>
            <p>角色: {{ userInfo.roleDisplay }}</p>
            <p>权限总数: {{ allPermissions.length }}</p>
            <p>已选权限: {{ selectedPermissions.length }}</p>
            <p>权限分类: {{ Object.keys(permissionCategories).length }}</p>
          </div>
          <!-- 简化的权限选择 -->
          <div class="simple-permission-selection">
            <div style="margin-bottom: 16px;">
              <ElButton type="primary" size="small" @click="selectAllPermissions">全选</ElButton>
              <ElButton size="small" @click="clearAllPermissions">清空</ElButton>
              <span style="margin-left: 16px;">已选择: {{ selectedPermissions.length }} / {{ allPermissions.length }}</span>
            </div>

            <div v-for="(permissions, category) in permissionCategories" :key="category" style="margin-bottom: 20px;">
              <div style="font-weight: bold; margin-bottom: 8px; color: #409eff;">{{ category }}</div>
              <ElCheckboxGroup v-model="selectedPermissions">
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px;">
                  <ElCheckbox
                    v-for="permission in permissions"
                    :key="permission.id"
                    :value="permission.id"
                    style="margin-bottom: 8px;"
                  >
                    {{ permission.name }}
                  </ElCheckbox>
                </div>
              </ElCheckboxGroup>
            </div>
          </div>
        </div>
      </ElCard>

      <!-- 会员关联管理（仅助理角色） -->
      <ElCard 
        v-if="userInfo.role === 'MEMBER_ASSISTANT'" 
        class="member-card" 
        shadow="never" 
        style="margin-top: 20px;"
      >
        <template #header>
          <div class="card-header">
            <span>负责会员管理</span>
            <div class="header-actions">
              <ElButton type="primary" size="small" @click="saveMemberAssignment" :loading="savingMembers">
                保存关联
              </ElButton>
            </div>
          </div>
        </template>

        <div class="member-assignment">
          <ElSelect 
            v-model="selectedMemberIds" 
            multiple 
            placeholder="选择负责的会员" 
            style="width: 100%"
            :max-collapse-tags="3"
          >
            <ElOption
              v-for="member in memberList"
              :key="member.id"
              :value="member.id"
              :label="`${member.username} (${member.email})`"
            >
              <div class="member-option">
                <span class="member-name">{{ member.username }}</span>
                <span class="member-email">{{ member.email }}</span>
              </div>
            </ElOption>
          </ElSelect>
          
          <div class="current-members" v-if="userInfo.responsibleMembers.length > 0">
            <div class="members-title">当前负责会员：</div>
            <div class="member-tags">
              <ElTag 
                v-for="member in userInfo.responsibleMembers" 
                :key="member.id"
                type="success"
                style="margin-right: 8px; margin-bottom: 8px;"
              >
                {{ member.username }}
              </ElTag>
            </div>
          </div>
        </div>
      </ElCard>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton type="primary" @click="saveAll" :loading="saving || savingMembers">
          保存所有更改
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ref, computed, watch, nextTick } from 'vue'
  import { ElMessage } from 'element-plus'
  import { UserService } from '@/api/usersApi'

  interface Props {
    visible: boolean
    userId: number | null
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void
    (e: 'success'): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 对话框显示控制
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => {
      console.log('权限管理对话框关闭:', value)
      emit('update:visible', value)
    }
  })

  // 数据状态
  const userInfo = ref<Api.User.UserDetailInfo>({
    id: 0,
    username: '',
    email: '',
    phone: '',
    role: '',
    roleDisplay: '',
    regionName: null,
    managedBy: null,
    permissions: [],
    responsibleMembers: []
  })
  const allPermissions = ref<Api.Common.PermissionInfo[]>([])
  const permissionCategories = ref<Record<string, Api.Common.PermissionInfo[]>>({})
  const memberList = ref<Api.Common.MemberInfo[]>([])

  // 选择状态
  const selectedPermissions = ref<number[]>([])
  const selectedMemberIds = ref<number[]>([])
  const originalPermissions = ref<number[]>([])
  const originalMemberIds = ref<number[]>([])

  // 筛选状态
  const selectedCategory = ref<string>('')

  // 加载状态
  const saving = ref(false)
  const savingMembers = ref(false)

  // 计算属性
  const categories = computed(() => Object.keys(permissionCategories.value))
  
  const filteredPermissionCategories = computed(() => {
    if (!selectedCategory.value) {
      return permissionCategories.value
    }
    return {
      [selectedCategory.value]: permissionCategories.value[selectedCategory.value] || []
    }
  })

  const permissionRatio = computed(() => {
    if (allPermissions.value.length === 0) return 0
    return Math.round((selectedPermissions.value.length / allPermissions.value.length) * 100)
  })

  const selectAll = computed({
    get: () => selectedPermissions.value.length === allPermissions.value.length,
    set: (value) => {
      if (value) {
        selectedPermissions.value = allPermissions.value.map(p => p.id)
      } else {
        selectedPermissions.value = []
      }
    }
  })

  const isIndeterminate = computed(() => {
    const selected = selectedPermissions.value.length
    return selected > 0 && selected < allPermissions.value.length
  })

  // 方法
  const loadUserDetail = async () => {
    if (!props.userId) {
      console.error('loadUserDetail: userId为空')
      return
    }

    console.log('开始加载用户详情，用户ID:', props.userId)

    try {
      const response = await UserService.getUserDetail(props.userId)
      console.log('用户详情API完整响应:', response)
      console.log('响应类型:', typeof response)
      console.log('响应键:', response ? Object.keys(response) : 'response为空')

      // 根据实际API响应格式提取数据
      // API响应格式: { code: 200, msg: "...", data: {...} }
      if (!response || !response.data) {
        console.error('API响应格式错误:', response)
        throw new Error('API响应数据为空或格式不正确')
      }

      const userData = response.data
      console.log('提取的用户数据:', userData)

      userInfo.value = {
        id: userData.id || 0,
        username: userData.username || '',
        email: userData.email || '',
        phone: userData.phone || '',  // 处理null值
        role: userData.role || '',
        roleDisplay: userData.roleDisplay || '',
        regionName: userData.regionName || null,
        managedBy: userData.managedBy || null,
        permissions: userData.permissions || [],
        responsibleMembers: userData.responsibleMembers || []
      }

      // 处理null值，确保字符串字段不为null
      if (userInfo.value.phone === null) userInfo.value.phone = ''
      if (userInfo.value.email === null) userInfo.value.email = ''

      // 设置初始选择状态
      const permissionIds = userInfo.value.permissions.map(p => p.id)
      const memberIds = userInfo.value.responsibleMembers.map(m => m.id)

      selectedPermissions.value = [...permissionIds]
      selectedMemberIds.value = [...memberIds]

      // 保存原始状态
      originalPermissions.value = [...permissionIds]
      originalMemberIds.value = [...memberIds]

      console.log('用户详情加载完成:', {
        username: userInfo.value.username,
        role: userInfo.value.role,
        selectedPermissions: selectedPermissions.value,
        selectedMembers: selectedMemberIds.value
      })

    } catch (error) {
      console.error('加载用户详情失败:', error)
      ElMessage.error(`加载用户详情失败: ${error.message || '未知错误'}`)
      throw error
    }
  }

  const loadPermissions = async () => {
    console.log('开始加载权限列表')

    try {
      const response = await UserService.getPermissionList()
      console.log('权限列表API响应:', response)

      if (!response) {
        throw new Error('权限API响应数据为空')
      }

      allPermissions.value = response.permissions || []
      permissionCategories.value = response.categories || {}

      console.log('权限列表加载完成:', {
        总权限数: allPermissions.value.length,
        分类数: Object.keys(permissionCategories.value).length,
        分类: Object.keys(permissionCategories.value)
      })

    } catch (error) {
      console.error('加载权限列表失败:', error)
      ElMessage.error(`加载权限列表失败: ${error.message || '未知错误'}`)
      throw error
    }
  }

  const loadMembers = async () => {
    console.log('检查是否需要加载会员列表，用户角色:', userInfo.value.role)

    if (userInfo.value.role !== 'MEMBER_ASSISTANT') {
      console.log('非助理角色，跳过会员列表加载')
      return
    }

    console.log('开始加载会员列表')

    try {
      const response = await UserService.getMemberList()
      console.log('会员列表API响应:', response)

      memberList.value = response || []

      console.log('会员列表加载完成:', {
        会员数量: memberList.value.length
      })

    } catch (error) {
      console.error('加载会员列表失败:', error)
      ElMessage.error(`加载会员列表失败: ${error.message || '未知错误'}`)
      // 不抛出错误，因为会员列表加载失败不应该阻止整个对话框
    }
  }

  const isCategorySelected = (category: string) => {
    const categoryPermissions = permissionCategories.value[category] || []
    return categoryPermissions.every(p => selectedPermissions.value.includes(p.id))
  }

  const isCategoryIndeterminate = (category: string) => {
    const categoryPermissions = permissionCategories.value[category] || []
    const selectedCount = categoryPermissions.filter(p => selectedPermissions.value.includes(p.id)).length
    return selectedCount > 0 && selectedCount < categoryPermissions.length
  }

  const selectAllPermissions = () => {
    const allIds = allPermissions.value.map(p => p.id)
    selectedPermissions.value = [...allIds]
    console.log('全选权限:', selectedPermissions.value)
    nextTick(() => {
      ElMessage.success(`已选择所有权限 (${allIds.length}个)`)
    })
  }

  const clearAllPermissions = () => {
    selectedPermissions.value = []
    ElMessage.info('已清空所有权限')
  }

  const handleCancel = () => {
    console.log('取消权限管理')
    dialogVisible.value = false
  }

  const savePermissions = async () => {
    if (!props.userId) {
      ElMessage.error('用户ID不存在')
      return
    }

    if (!userInfo.value || !userInfo.value.username) {
      ElMessage.error('用户信息未加载，请重新打开对话框')
      console.error('userInfo未正确加载:', userInfo.value)
      return
    }

    console.log('开始保存权限:', {
      userId: props.userId,
      userInfo: userInfo.value,
      selectedPermissions: selectedPermissions.value,
      originalPermissions: originalPermissions.value
    })

    saving.value = true
    try {
      const updateData = {
        username: userInfo.value.username,
        email: userInfo.value.email || '',
        phone: userInfo.value.phone || '',
        role: userInfo.value.role,
        permission_ids: selectedPermissions.value
      }

      console.log('发送更新数据:', updateData)

      await UserService.updateUser(props.userId, updateData)
      originalPermissions.value = [...selectedPermissions.value]

      console.log('权限保存成功')
      ElMessage.success(`权限保存成功 (${selectedPermissions.value.length}个权限)`)
    } catch (error) {
      console.error('保存权限失败:', error)
      ElMessage.error(`保存权限失败: ${error.message || '未知错误'}`)
    } finally {
      saving.value = false
    }
  }

  const saveMemberAssignment = async () => {
    if (!props.userId || userInfo.value.role !== 'MEMBER_ASSISTANT') return
    
    savingMembers.value = true
    try {
      const updateData = {
        username: userInfo.value.username,
        email: userInfo.value.email,
        phone: userInfo.value.phone,
        role: userInfo.value.role,
        permission_ids: selectedPermissions.value,
        responsible_member_ids: selectedMemberIds.value
      }
      
      await UserService.updateUser(props.userId, updateData)
      originalMemberIds.value = [...selectedMemberIds.value]
      
      // 重新加载用户详情以更新显示
      await loadUserDetail()
      
      ElMessage.success('会员关联保存成功')
    } catch (error) {
      console.error('保存会员关联失败:', error)
      ElMessage.error('保存会员关联失败')
    } finally {
      savingMembers.value = false
    }
  }

  const saveAll = async () => {
    await Promise.all([
      savePermissions(),
      userInfo.value.role === 'MEMBER_ASSISTANT' ? saveMemberAssignment() : Promise.resolve()
    ])
    
    emit('success')
    dialogVisible.value = false
  }

  const resetPermissions = () => {
    selectedPermissions.value = [...originalPermissions.value]
    selectedMemberIds.value = [...originalMemberIds.value]
    ElMessage.info('已重置为原始状态')
  }

  // 监听对话框显示状态
  watch(() => props.visible, async (visible) => {
    if (visible && props.userId) {
      console.log('权限管理对话框打开，用户ID:', props.userId)

      // 重置数据状态
      userInfo.value = {
        id: 0,
        username: '',
        email: '',
        phone: '',
        role: '',
        roleDisplay: '',
        regionName: null,
        managedBy: null,
        permissions: [],
        responsibleMembers: []
      }
      selectedPermissions.value = []
      selectedMemberIds.value = []
      allPermissions.value = []
      permissionCategories.value = {}
      memberList.value = []

      try {
        // 分别加载数据，不因单个失败而中断
        console.log('开始加载用户详情...')
        try {
          await loadUserDetail()
          console.log('用户详情加载成功')
        } catch (error) {
          console.error('用户详情加载失败:', error)
          ElMessage.error('用户详情加载失败')
        }

        console.log('开始加载权限列表...')
        try {
          await loadPermissions()
          console.log('权限列表加载成功')
        } catch (error) {
          console.error('权限列表加载失败:', error)
          ElMessage.error('权限列表加载失败')
        }

        console.log('开始加载会员列表...')
        try {
          await loadMembers()
          console.log('会员列表加载成功')
        } catch (error) {
          console.error('会员列表加载失败:', error)
          // 会员列表失败不显示错误，因为不是所有用户都需要
        }

        console.log('权限管理数据加载完成:', {
          userInfo: userInfo.value,
          selectedPermissions: selectedPermissions.value,
          allPermissions: allPermissions.value.length,
          categories: Object.keys(permissionCategories.value)
        })
      } catch (error) {
        console.error('权限管理数据加载失败:', error)
        ElMessage.error('数据加载失败，请重试')
      }
    }
  }, { immediate: true })

  // 监听用户ID变化
  watch(() => props.userId, async (newUserId) => {
    if (newUserId && props.visible) {
      console.log('用户ID变化，重新加载数据:', newUserId)
      await loadUserDetail()
    }
  })
</script>

<style lang="scss" scoped>
  .permission-management {
    .user-info-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    .permission-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .header-actions {
          display: flex;
          gap: 8px;
        }
      }

      .super-admin-notice {
        text-align: center;
        padding: 20px;
      }

      .permission-content {
        .permission-stats {
          display: flex;
          gap: 40px;
          margin-bottom: 20px;
          padding: 16px;
          background: #f5f7fa;
          border-radius: 6px;
        }

        .permission-selection {
          .selection-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #ebeef5;
            
            .category-filter {
              width: 200px;
            }
          }

          .permission-groups {
            .permission-group {
              margin-bottom: 24px;
              
              .group-header {
                display: flex;
                align-items: center;
                margin-bottom: 12px;
                font-weight: 600;
                color: #409eff;
                
                .group-count {
                  margin-left: 8px;
                  font-size: 12px;
                  color: #909399;
                }
              }

              .group-permissions {
                .permission-grid {
                  display: grid;
                  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                  gap: 12px;
                  
                  .permission-item {
                    border: 1px solid #e4e7ed;
                    border-radius: 6px;
                    padding: 12px;
                    transition: all 0.3s;
                    
                    &:hover {
                      border-color: #409eff;
                      background: #f0f9ff;
                    }
                    
                    .permission-info {
                      .permission-name {
                        font-weight: 500;
                        color: #303133;
                        margin-bottom: 4px;
                      }
                      
                      .permission-desc {
                        font-size: 12px;
                        color: #909399;
                        line-height: 1.4;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    .member-card {
      .member-assignment {
        .member-option {
          display: flex;
          justify-content: space-between;
          
          .member-name {
            font-weight: 500;
          }
          
          .member-email {
            color: #909399;
            font-size: 12px;
          }
        }
        
        .current-members {
          margin-top: 16px;
          
          .members-title {
            margin-bottom: 8px;
            font-weight: 500;
            color: #606266;
          }
          
          .member-tags {
            display: flex;
            flex-wrap: wrap;
          }
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
</style>
