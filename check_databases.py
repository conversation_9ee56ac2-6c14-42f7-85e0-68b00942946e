#!/usr/bin/env python3
import os
import sys
import django

# 设置Django环境
sys.path.append('ep_system')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ep_system.settings')
django.setup()

from django.db import connections
from core.models import CustomUser, Region
from business.models import Customer

def check_databases():
    print('🗄️ 当前数据库配置:')
    print('=' * 50)
    
    for alias in connections:
        db = connections[alias]
        db_name = db.settings_dict['NAME']
        print(f'  {alias}: {db_name}')
    
    print('\n📊 数据库使用情况:')
    print('=' * 50)
    
    # 检查主数据库中的用户
    print('👥 主数据库用户 (ep_system_master):')
    users = CustomUser.objects.all()
    for user in users:
        region_name = user.region.name if user.region else '全局'
        print(f'  - {user.username} ({user.role}) - {region_name}')
    
    print('\n🏢 区域配置:')
    regions = Region.objects.all()
    for region in regions:
        print(f'  - {region.name} ({region.code})')
    
    print('\n💼 业务数据分布:')
    # 检查各区域数据库中的客户数据
    region_dbs = ['mpr_db', 'rl_db', 'eo_db', 'zz_db', 'wh_db']
    for db_alias in region_dbs:
        try:
            customers = Customer.objects.using(db_alias).all()
            print(f'  - {db_alias}: {len(customers)} 个客户')
        except Exception as e:
            print(f'  - {db_alias}: 无法访问 ({str(e)[:50]}...)')

if __name__ == '__main__':
    check_databases()
