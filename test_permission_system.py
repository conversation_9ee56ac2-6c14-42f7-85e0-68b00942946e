#!/usr/bin/env python3
import requests
import json

# 测试权限管理系统
base_url = 'http://localhost:8000'

def login(username, password):
    """登录获取token"""
    login_data = {
        'userName': username,
        'password': password
    }
    
    response = requests.post(f'{base_url}/api/v1/auth/login/', 
        data=login_data,
        headers={'X-Region-Code': 'MPR'}
    )
    
    if response.status_code == 200:
        return response.json()['data']['token']
    else:
        raise Exception(f"登录失败: {response.text}")

def get_permissions(token):
    """获取权限列表"""
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR'
    }
    
    response = requests.get(f'{base_url}/api/v1/user/permissions/', headers=headers)
    return response

def get_members(token):
    """获取会员列表"""
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR'
    }
    
    response = requests.get(f'{base_url}/api/v1/user/members/', headers=headers)
    return response

def create_user_with_permissions(token, user_data):
    """创建用户并分配权限"""
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR',
        'Content-Type': 'application/json'
    }
    
    response = requests.post(f'{base_url}/api/v1/user/list/', 
        json=user_data,
        headers=headers
    )
    return response

def main():
    print("🧪 测试权限管理系统")
    print("=" * 50)
    
    try:
        # 1. 登录
        print("🔐 登录系统...")
        token = login('mpr_admin', 'admin123')
        print("✅ 登录成功!")
        
        # 2. 测试获取权限列表
        print("\n📋 测试获取权限列表...")
        response = get_permissions(token)
        print(f"权限列表API状态码: {response.status_code}")
        
        if response.status_code == 200:
            permissions_data = response.json()['data']
            permissions = permissions_data['permissions']
            categories = permissions_data['categories']
            
            print(f"✅ 获取到 {len(permissions)} 个权限")
            print("权限分类:")
            for category, perms in categories.items():
                print(f"  📁 {category}: {len(perms)} 个权限")
                for perm in perms[:2]:  # 只显示前2个
                    print(f"    - {perm['name']} ({perm['code']})")
                if len(perms) > 2:
                    print(f"    ... 还有 {len(perms) - 2} 个权限")
        else:
            print(f"❌ 获取权限列表失败: {response.text}")
            return
        
        # 3. 测试获取会员列表
        print("\n👥 测试获取会员列表...")
        response = get_members(token)
        print(f"会员列表API状态码: {response.status_code}")
        
        if response.status_code == 200:
            members = response.json()['data']
            print(f"✅ 获取到 {len(members)} 个会员")
            for member in members[:3]:  # 只显示前3个
                managed_by = member.get('managed_by')
                managed_info = f" (由 {managed_by['username']} 负责)" if managed_by else " (无助理负责)"
                print(f"  - {member['username']} ({member['email']}){managed_info}")
        else:
            print(f"❌ 获取会员列表失败: {response.text}")
            members = []
        
        # 4. 测试创建用户并分配权限
        print("\n👤 测试创建用户并分配权限...")
        
        # 选择一些权限ID
        selected_permission_ids = [perm['id'] for perm in permissions[:3]]  # 选择前3个权限
        
        import time
        timestamp = int(time.time())
        
        user_data = {
            'username': f'test_perm_user_{timestamp}',
            'email': f'test_perm_{timestamp}@example.com',
            'password': 'password123',
            'role': 'WAREHOUSE_MANAGER',
            'permission_ids': selected_permission_ids
        }
        
        response = create_user_with_permissions(token, user_data)
        print(f"创建用户状态码: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()['data']
            print("✅ 用户创建成功!")
            print(f"  用户名: {result['username']}")
            print(f"  角色: {result['roleDisplay']}")
            print(f"  分配权限数: {result['permissions_assigned']}")
            print(f"  默认密码: {result['defaultPassword']}")
        else:
            print(f"❌ 创建用户失败: {response.text}")
        
        # 5. 测试创建助理并关联会员
        if members:
            print("\n🤝 测试创建助理并关联会员...")
            
            # 选择前2个会员
            selected_member_ids = [member['id'] for member in members[:2]]
            
            assistant_data = {
                'username': f'test_assistant_{timestamp}',
                'email': f'test_assistant_{timestamp}@example.com',
                'password': 'password123',
                'role': 'MEMBER_ASSISTANT',
                'permission_ids': selected_permission_ids[:2],  # 分配2个权限
                'responsible_member_ids': selected_member_ids  # 负责2个会员
            }
            
            response = create_user_with_permissions(token, assistant_data)
            print(f"创建助理状态码: {response.status_code}")
            
            if response.status_code == 201:
                result = response.json()['data']
                print("✅ 助理创建成功!")
                print(f"  助理用户名: {result['username']}")
                print(f"  角色: {result['roleDisplay']}")
                print(f"  分配权限数: {result['permissions_assigned']}")
                print(f"  负责会员数: {result['members_assigned']}")
            else:
                print(f"❌ 创建助理失败: {response.text}")
        
        print("\n🎉 权限管理系统测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
