#!/usr/bin/env python3
import requests
import json

# 测试权限管理对话框功能
base_url = 'http://localhost:8000'

def login(username, password):
    """登录获取token"""
    login_data = {
        'userName': username,
        'password': password
    }
    
    response = requests.post(f'{base_url}/api/v1/auth/login/', 
        data=login_data,
        headers={'X-Region-Code': 'MPR'}
    )
    
    if response.status_code == 200:
        return response.json()['data']['token']
    else:
        raise Exception(f"登录失败: {response.text}")

def test_permission_management_apis(token):
    """测试权限管理相关的API"""
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR'
    }
    
    print("🔍 测试权限管理相关API...")
    
    # 1. 获取用户列表
    print("1️⃣ 获取用户列表...")
    users_response = requests.get(f'{base_url}/api/v1/user/list/', headers=headers)
    if users_response.status_code == 200:
        users = users_response.json()['data']['records']
        print(f"✅ 用户列表获取成功，共 {len(users)} 个用户")
        
        # 选择一个非超级管理员用户进行测试
        test_user = None
        for user in users:
            if user.get('role') != 'SUPER_ADMIN':
                test_user = user
                break
        
        if not test_user:
            print("❌ 没有找到可测试的用户")
            return False
        
        user_id = test_user['id']
        print(f"📋 选择测试用户: {test_user['userName']} (ID: {user_id})")
        
        # 2. 获取用户详情
        print("2️⃣ 获取用户详情...")
        detail_response = requests.get(f'{base_url}/api/v1/user/{user_id}/', headers=headers)
        if detail_response.status_code == 200:
            user_detail = detail_response.json()['data']
            print(f"✅ 用户详情获取成功")
            print(f"   用户名: {user_detail['username']}")
            print(f"   角色: {user_detail['roleDisplay']}")
            print(f"   当前权限数: {len(user_detail['permissions'])}")
            print(f"   负责会员数: {len(user_detail['responsibleMembers'])}")
        else:
            print(f"❌ 用户详情获取失败: {detail_response.text}")
            return False
        
        # 3. 获取权限列表
        print("3️⃣ 获取权限列表...")
        perm_response = requests.get(f'{base_url}/api/v1/user/permissions/', headers=headers)
        if perm_response.status_code == 200:
            perm_data = perm_response.json()['data']
            permissions = perm_data['permissions']
            categories = perm_data['categories']
            print(f"✅ 权限列表获取成功")
            print(f"   权限总数: {len(permissions)}")
            print(f"   权限分类: {len(categories)} 个")
            for category, perms in categories.items():
                print(f"     - {category}: {len(perms)} 个权限")
        else:
            print(f"❌ 权限列表获取失败: {perm_response.text}")
            return False
        
        # 4. 获取会员列表（如果是助理角色）
        if user_detail['role'] == 'MEMBER_ASSISTANT':
            print("4️⃣ 获取会员列表...")
            member_response = requests.get(f'{base_url}/api/v1/user/members/', headers=headers)
            if member_response.status_code == 200:
                members = member_response.json()['data']
                print(f"✅ 会员列表获取成功，共 {len(members)} 个会员")
            else:
                print(f"❌ 会员列表获取失败: {member_response.text}")
                return False
        
        # 5. 测试权限更新
        print("5️⃣ 测试权限更新...")
        
        # 选择新的权限
        current_permission_ids = [p['id'] for p in user_detail['permissions']]
        available_permissions = [p for p in permissions if p['id'] not in current_permission_ids]
        new_permission_ids = current_permission_ids + [p['id'] for p in available_permissions[:3]]
        
        update_data = {
            'username': user_detail['username'],
            'email': user_detail['email'],
            'phone': user_detail['phone'],
            'role': user_detail['role'],
            'permission_ids': new_permission_ids
        }
        
        # 如果是助理，保持会员关联
        if user_detail['role'] == 'MEMBER_ASSISTANT':
            update_data['responsible_member_ids'] = [m['id'] for m in user_detail['responsibleMembers']]
        
        update_response = requests.put(f'{base_url}/api/v1/user/{user_id}/', 
            json=update_data, headers=headers)
        
        if update_response.status_code == 200:
            print(f"✅ 权限更新成功")
            print(f"   权限数量: {len(current_permission_ids)} → {len(new_permission_ids)}")
            
            # 验证更新结果
            verify_response = requests.get(f'{base_url}/api/v1/user/{user_id}/', headers=headers)
            if verify_response.status_code == 200:
                updated_detail = verify_response.json()['data']
                actual_permissions = len(updated_detail['permissions'])
                print(f"✅ 更新验证成功，实际权限数: {actual_permissions}")
                return actual_permissions == len(new_permission_ids)
            else:
                print(f"❌ 更新验证失败: {verify_response.text}")
                return False
        else:
            print(f"❌ 权限更新失败: {update_response.text}")
            return False
    
    else:
        print(f"❌ 用户列表获取失败: {users_response.text}")
        return False

def main():
    print("🧪 测试权限管理对话框功能")
    print("=" * 60)
    
    try:
        # 登录
        print("🔐 登录系统...")
        token = login('mpr_admin', 'admin123')
        print("✅ 登录成功!")
        
        # 测试权限管理API
        print("\n🔄 测试权限管理API...")
        api_success = test_permission_management_apis(token)
        
        if api_success:
            print("\n🎉 权限管理API测试成功！")
            print("\n📋 前端测试步骤:")
            print("1. 打开 http://localhost:3010/#/system/user")
            print("2. 点击任意用户的'权限'按钮")
            print("3. 查看权限管理对话框是否正常显示")
            print("4. 检查调试信息是否显示正确数据")
            print("5. 测试权限选择和保存功能")
            print("6. 如果是助理角色，测试会员关联功能")
            
            print("\n🔧 如果权限管理对话框仍然无法交互，请检查:")
            print("- 浏览器控制台是否有JavaScript错误")
            print("- 网络请求是否正常")
            print("- Vue组件是否正确加载")
        else:
            print("\n❌ 权限管理API测试失败！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
