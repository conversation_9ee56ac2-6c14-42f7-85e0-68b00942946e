#!/usr/bin/env python3
"""
创建测试用户和数据
用于验证第三阶段的精细化权限控制
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ep_system.settings')
django.setup()

from core.models import CustomUser, Region
from business.models import Customer, Product, Order
from django.contrib.auth.hashers import make_password


def create_test_users():
    """创建测试用户"""
    print("🔧 创建测试用户...")
    
    # 获取区域
    try:
        mpr_region = Region.objects.get(code='MPR')
        zz_region = Region.objects.get(code='ZZ')
    except Region.DoesNotExist:
        print("❌ 区域不存在，请先运行数据库迁移")
        return
    
    # 创建测试用户
    test_users = [
        # MPR区域用户
        {
            'username': 'mpr_member1',
            'password': 'test123',
            'email': '<EMAIL>',
            'role': CustomUser.Role.MEMBER,
            'region': mpr_region,
            'first_name': 'MPR会员1'
        },
        {
            'username': 'mpr_member2', 
            'password': 'test123',
            'email': '<EMAIL>',
            'role': CustomUser.Role.MEMBER,
            'region': mpr_region,
            'first_name': 'MPR会员2'
        },
        {
            'username': 'mpr_assistant',
            'password': 'test123',
            'email': '<EMAIL>',
            'role': CustomUser.Role.MEMBER_ASSISTANT,
            'region': mpr_region,
            'first_name': 'MPR会员助理'
        },
        {
            'username': 'mpr_warehouse',
            'password': 'test123',
            'email': '<EMAIL>',
            'role': CustomUser.Role.WAREHOUSE_MANAGER,
            'region': mpr_region,
            'first_name': 'MPR库管'
        },
        # ZZ区域用户
        {
            'username': 'zz_member1',
            'password': 'test123',
            'email': '<EMAIL>',
            'role': CustomUser.Role.MEMBER,
            'region': zz_region,
            'first_name': 'ZZ会员1'
        },
    ]
    
    created_users = {}
    for user_data in test_users:
        user, created = CustomUser.objects.get_or_create(
            username=user_data['username'],
            defaults={
                'password': make_password(user_data['password']),
                'email': user_data['email'],
                'role': user_data['role'],
                'region': user_data['region'],
                'first_name': user_data['first_name'],
            }
        )
        created_users[user_data['username']] = user
        status = "✅ 创建" if created else "🔄 已存在"
        print(f"  {status} {user.first_name} ({user.username}) - {user.get_role_display()}")
    
    return created_users


def create_test_data(users):
    """创建测试数据"""
    print("\n📊 创建测试数据...")
    
    # 模拟不同用户创建数据
    mpr_member1 = users.get('mpr_member1')
    mpr_member2 = users.get('mpr_member2')
    zz_member1 = users.get('zz_member1')
    
    if not all([mpr_member1, mpr_member2, zz_member1]):
        print("❌ 测试用户创建失败")
        return
    
    # 使用MPR数据库创建客户数据
    print("  📋 创建客户数据...")
    customers_data = [
        {
            'customer_code': 'MPR-C001',
            'name': 'MPR客户A',
            'contact_person': '张三',
            'phone': '13800138001',
            'created_by_id': mpr_member1.id,
        },
        {
            'customer_code': 'MPR-C002', 
            'name': 'MPR客户B',
            'contact_person': '李四',
            'phone': '13800138002',
            'created_by_id': mpr_member2.id,
        },
    ]
    
    for customer_data in customers_data:
        customer, created = Customer.objects.using('mpr_db').get_or_create(
            customer_code=customer_data['customer_code'],
            defaults=customer_data
        )
        status = "✅ 创建" if created else "🔄 已存在"
        creator = "会员1" if customer.created_by_id == mpr_member1.id else "会员2"
        print(f"    {status} {customer.name} (创建者: {creator})")
    
    # 创建产品数据
    print("  📦 创建产品数据...")
    products_data = [
        {
            'product_code': 'MPR-P001',
            'name': '产品A',
            'description': '会员1创建的产品',
            'price': 100.00,
            'created_by_id': mpr_member1.id,
        },
        {
            'product_code': 'MPR-P002',
            'name': '产品B', 
            'description': '会员2创建的产品',
            'price': 200.00,
            'created_by_id': mpr_member2.id,
        },
    ]
    
    for product_data in products_data:
        product, created = Product.objects.using('mpr_db').get_or_create(
            product_code=product_data['product_code'],
            defaults=product_data
        )
        status = "✅ 创建" if created else "🔄 已存在"
        creator = "会员1" if product.created_by_id == mpr_member1.id else "会员2"
        print(f"    {status} {product.name} (创建者: {creator})")


def test_user_permissions():
    """测试用户权限"""
    print("\n🔍 测试用户权限...")
    
    # 获取测试用户
    try:
        mpr_member1 = CustomUser.objects.get(username='mpr_member1')
        mpr_member2 = CustomUser.objects.get(username='mpr_member2')
        mpr_admin = CustomUser.objects.get(username='mpr_admin')
    except CustomUser.DoesNotExist:
        print("❌ 测试用户不存在")
        return
    
    # 测试数据访问权限
    print("  📊 测试数据访问权限...")
    
    # 获取所有客户（使用不同用户的管理器）
    all_customers = Customer.all_objects.using('mpr_db').all()
    print(f"    总客户数: {all_customers.count()}")

    # 模拟不同用户的数据访问
    from core.middleware import _thread_locals

    # 模拟会员1登录
    _thread_locals.user = mpr_member1
    member1_customers = Customer.objects.using('mpr_db').all()
    print(f"    会员1可见客户数: {member1_customers.count()}")

    # 模拟会员2登录
    _thread_locals.user = mpr_member2
    member2_customers = Customer.objects.using('mpr_db').all()
    print(f"    会员2可见客户数: {member2_customers.count()}")

    # 模拟管理员登录
    _thread_locals.user = mpr_admin
    admin_customers = Customer.objects.using('mpr_db').all()
    print(f"    管理员可见客户数: {admin_customers.count()}")
    
    # 清理
    if hasattr(_thread_locals, 'user'):
        delattr(_thread_locals, 'user')


if __name__ == '__main__':
    print("🚀 开始创建测试用户和数据...")
    
    # 创建测试用户
    users = create_test_users()
    
    # 创建测试数据
    create_test_data(users)
    
    # 测试权限
    test_user_permissions()
    
    print("\n✅ 测试用户和数据创建完成！")
    print("\n📋 测试账户信息:")
    print("  - mpr_member1 / test123 (MPR普通会员)")
    print("  - mpr_member2 / test123 (MPR普通会员)")
    print("  - mpr_assistant / test123 (MPR会员助理)")
    print("  - mpr_warehouse / test123 (MPR库管)")
    print("  - zz_member1 / test123 (ZZ普通会员)")
