#!/usr/bin/env python3
import requests
import json

# 测试权限管理API响应格式
base_url = 'http://localhost:8000'

def login(username, password):
    """登录获取token"""
    login_data = {
        'userName': username,
        'password': password
    }
    
    response = requests.post(f'{base_url}/api/v1/auth/login/', 
        data=login_data,
        headers={'X-Region-Code': 'MPR'}
    )
    
    if response.status_code == 200:
        return response.json()['data']['token']
    else:
        raise Exception(f"登录失败: {response.text}")

def test_api_response_format(token):
    """测试API响应格式"""
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR'
    }
    
    print("🔍 测试API响应格式...")
    
    # 获取用户列表
    users_response = requests.get(f'{base_url}/api/v1/user/list/', headers=headers)
    if users_response.status_code == 200:
        users = users_response.json()['data']['records']
        test_user = users[1] if len(users) > 1 else users[0]
        user_id = test_user['id']
        
        print(f"📋 测试用户: {test_user['userName']} (ID: {user_id})")
        
        # 测试用户详情API的响应格式
        print("🔍 测试用户详情API响应格式...")
        detail_response = requests.get(f'{base_url}/api/v1/user/{user_id}/', headers=headers)
        
        print(f"状态码: {detail_response.status_code}")
        print(f"响应头: {dict(detail_response.headers)}")
        
        if detail_response.status_code == 200:
            response_json = detail_response.json()
            print(f"完整响应结构:")
            print(json.dumps(response_json, indent=2, ensure_ascii=False))
            
            print(f"\n响应根级键: {list(response_json.keys())}")
            
            if 'data' in response_json:
                data = response_json['data']
                print(f"data字段类型: {type(data)}")
                print(f"data字段键: {list(data.keys()) if isinstance(data, dict) else 'N/A'}")
                
                # 检查是否有嵌套的data字段
                if isinstance(data, dict) and 'data' in data:
                    print(f"嵌套data字段存在")
                    nested_data = data['data']
                    print(f"嵌套data类型: {type(nested_data)}")
                    print(f"嵌套data键: {list(nested_data.keys()) if isinstance(nested_data, dict) else 'N/A'}")
                else:
                    print(f"无嵌套data字段")
                
                # 检查用户信息字段
                user_data = data.get('data', data) if isinstance(data, dict) and 'data' in data else data
                if isinstance(user_data, dict):
                    required_fields = ['username', 'email', 'phone', 'role', 'permissions']
                    print(f"\n用户数据字段检查:")
                    for field in required_fields:
                        value = user_data.get(field)
                        print(f"  {field}: {value} (类型: {type(value)})")
            
            return True
        else:
            print(f"❌ API调用失败: {detail_response.text}")
            return False
    else:
        print(f"❌ 获取用户列表失败: {users_response.text}")
        return False

def main():
    print("🧪 测试权限管理API响应格式")
    print("=" * 60)
    
    try:
        # 登录
        print("🔐 登录系统...")
        token = login('mpr_admin', 'admin123')
        print("✅ 登录成功!")
        
        # 测试API响应格式
        print("\n🔄 测试API响应格式...")
        success = test_api_response_format(token)
        
        if success:
            print("\n✅ API响应格式测试完成！")
            print("\n📋 前端调试建议:")
            print("1. 检查前端代码中的响应数据提取逻辑")
            print("2. 确认是否需要使用 response.data.data 还是 response.data")
            print("3. 添加更多调试日志来跟踪数据流")
            print("4. 验证类型定义是否与实际响应匹配")
        else:
            print("\n❌ API响应格式测试失败！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
