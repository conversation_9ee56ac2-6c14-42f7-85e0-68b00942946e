# core/management/commands/init_databases.py
"""
初始化多数据库的管理命令
"""

from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.db import connections
from django.conf import settings
import pymysql


class Command(BaseCommand):
    help = '初始化所有区域数据库'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-databases',
            action='store_true',
            help='创建数据库（如果不存在）',
        )
        parser.add_argument(
            '--migrate-all',
            action='store_true',
            help='对所有数据库执行迁移',
        )
        parser.add_argument(
            '--reset-all',
            action='store_true',
            help='重置所有数据库（危险操作）',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🚀 开始初始化多数据库架构...')
        )

        if options['create_databases']:
            self.create_databases()

        if options['migrate_all']:
            self.migrate_all_databases()

        if options['reset_all']:
            self.reset_all_databases()

        self.stdout.write(
            self.style.SUCCESS('✅ 多数据库初始化完成！')
        )

    def create_databases(self):
        """创建所有数据库"""
        self.stdout.write('📊 创建数据库...')
        
        # 数据库配置
        db_configs = {
            'ep_system_master': '主数据库（用户认证、区域配置）',
            'ep_system_mpr': 'MPR区域数据库',
            'ep_system_rl': 'RL区域数据库',
            'ep_system_eo': 'EO区域数据库',
            'ep_system_zz': 'ZZ区域数据库（郑州）',
            'ep_system_wh': 'WH区域数据库（武汉）',
        }

        # 获取数据库连接参数
        default_db = settings.DATABASES['default']
        
        try:
            # 连接到MySQL服务器（不指定数据库）
            connection = pymysql.connect(
                host=default_db['HOST'],
                port=int(default_db['PORT']),
                user=default_db['USER'],
                password=default_db['PASSWORD'],
                charset='utf8mb4'
            )
            
            cursor = connection.cursor()
            
            for db_name, description in db_configs.items():
                try:
                    # 检查数据库是否存在
                    cursor.execute(f"SHOW DATABASES LIKE '{db_name}'")
                    if cursor.fetchone():
                        self.stdout.write(f'  ✓ {db_name} ({description}) - 已存在')
                    else:
                        # 创建数据库
                        cursor.execute(f"CREATE DATABASE {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                        self.stdout.write(
                            self.style.SUCCESS(f'  ✅ {db_name} ({description}) - 创建成功')
                        )
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'  ❌ {db_name} 创建失败: {e}')
                    )
            
            cursor.close()
            connection.close()
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ 数据库连接失败: {e}')
            )

    def migrate_all_databases(self):
        """对所有数据库执行迁移"""
        self.stdout.write('🔄 执行数据库迁移...')
        
        # 数据库别名列表
        databases = ['default', 'mpr_db', 'rl_db', 'eo_db', 'zz_db', 'wh_db']
        
        for db_alias in databases:
            try:
                self.stdout.write(f'  📋 迁移数据库: {db_alias}')
                
                # 执行迁移
                call_command('migrate', database=db_alias, verbosity=0)
                
                self.stdout.write(
                    self.style.SUCCESS(f'  ✅ {db_alias} 迁移完成')
                )
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'  ❌ {db_alias} 迁移失败: {e}')
                )

    def reset_all_databases(self):
        """重置所有数据库（危险操作）"""
        self.stdout.write(
            self.style.WARNING('⚠️  警告：这将删除所有数据库中的数据！')
        )
        
        confirm = input('确认要重置所有数据库吗？输入 "yes" 确认: ')
        if confirm.lower() != 'yes':
            self.stdout.write('操作已取消')
            return

        # 数据库配置
        db_configs = {
            'ep_system_master': '主数据库',
            'ep_system_mpr': 'MPR区域数据库',
            'ep_system_rl': 'RL区域数据库',
            'ep_system_eo': 'EO区域数据库',
            'ep_system_zz': 'ZZ区域数据库',
            'ep_system_wh': 'WH区域数据库',
        }

        # 获取数据库连接参数
        default_db = settings.DATABASES['default']
        
        try:
            connection = pymysql.connect(
                host=default_db['HOST'],
                port=int(default_db['PORT']),
                user=default_db['USER'],
                password=default_db['PASSWORD'],
                charset='utf8mb4'
            )
            
            cursor = connection.cursor()
            
            for db_name, description in db_configs.items():
                try:
                    # 删除数据库
                    cursor.execute(f"DROP DATABASE IF EXISTS {db_name}")
                    self.stdout.write(f'  🗑️  {db_name} ({description}) - 已删除')
                    
                    # 重新创建数据库
                    cursor.execute(f"CREATE DATABASE {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                    self.stdout.write(f'  ✅ {db_name} ({description}) - 重新创建')
                    
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'  ❌ {db_name} 重置失败: {e}')
                    )
            
            cursor.close()
            connection.close()
            
            # 重新执行迁移
            self.migrate_all_databases()
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ 数据库重置失败: {e}')
            )
