import request from '@/utils/http'

export class UserService {
  // 登录
  static login(params: Api.Auth.LoginParams) {
    return request.post<Api.Auth.LoginResponse>({
      url: '/api/v1/auth/login/',
      params
      // showErrorMessage: false // 不显示错误消息
    })
  }

  // 获取用户信息
  static getUserInfo() {
    return request.get<Api.User.UserInfo>({
      url: '/api/v1/user/info/'
    })
  }

  // 获取用户列表
  static getUserList(params: Api.Common.PaginatingSearchParams) {
    return request.get<Api.User.UserListData>({
      url: '/api/v1/user/list/',
      params
    })
  }

  // 新增用户
  static createUser(params: Api.User.CreateUserParams) {
    return request.post<Api.User.CreateUserResponse>({
      url: '/api/v1/user/list/',
      params
    })
  }

  // 获取用户详情
  static getUserDetail(id: number) {
    return request.get<Api.User.UserDetailResponse>({
      url: `/api/v1/user/${id}/`
    })
  }

  // 更新用户
  static updateUser(id: number, params: Api.User.UpdateUserParams) {
    return request.put<Api.User.UpdateUserResponse>({
      url: `/api/v1/user/${id}/`,
      data: params
    })
  }

  // 删除用户
  static deleteUser(id: number) {
    return request.del<Api.Common.CommonResponse>({
      url: `/api/v1/user/${id}/`
    })
  }

  // 获取助理列表
  static getAssistantList() {
    return request.get<Api.Common.AssistantInfo[]>({
      url: '/api/v1/user/assistants/'
    })
  }

  // 获取权限列表
  static getPermissionList() {
    return request.get<Api.Common.PermissionListResponse>({
      url: '/api/v1/user/permissions/'
    })
  }

  // 获取会员列表（用于助理选择）
  static getMemberList() {
    return request.get<Api.Common.MemberInfo[]>({
      url: '/api/v1/user/members/'
    })
  }
}
