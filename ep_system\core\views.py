from django.shortcuts import render
from django.contrib.auth import authenticate
from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from .serializers import UserRegistrationSerializer
from .models import CustomUser

# Create your views here.

class RegistrationAPIView(generics.CreateAPIView):
    """
    用户注册API视图
    
    支持区域感知的用户注册功能：
    - 自动关联请求头中X-Region-Code对应的区域
    - 默认角色为MEMBER（普通会员）
    - 在当前区域内验证用户名和邮箱的唯一性
    - 不需要认证即可访问
    """
    
    queryset = CustomUser.objects.all()
    serializer_class = UserRegistrationSerializer
    permission_classes = [AllowAny]  # 允许未认证用户访问
    
    def create(self, request, *args, **kwargs):
        """处理用户注册请求"""
        
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            user = serializer.save()
            
            return Response({
                'success': True,
                'message': '用户注册成功',
                'data': serializer.data
            }, status=status.HTTP_201_CREATED)
        
        return Response({
            'success': False,
            'message': '注册失败，请检查输入信息',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def get_serializer_context(self):
        """为序列化器提供上下文信息"""
        context = super().get_serializer_context()
        context['request'] = self.request
        return context


class LoginAPIView(APIView):
    """
    用户登录API视图

    接收用户名和密码，返回JWT token
    """
    permission_classes = [AllowAny]

    def post(self, request):
        """处理分区域登录请求"""
        username = request.data.get('userName')  # 前端发送的是userName
        password = request.data.get('password')

        if not username or not password:
            return Response({
                'code': 400,
                'msg': '用户名和密码不能为空',
                'data': None
            }, status=status.HTTP_400_BAD_REQUEST)

        # 直接查询用户，绕过区域限制，使用unfiltered查询
        try:
            # 使用unfiltered()方法绕过RegionAwareManager的过滤
            user = CustomUser.objects.unfiltered().get(username=username)

            if user.check_password(password):
                # 生成JWT token
                refresh = RefreshToken.for_user(user)

                # 构建用户信息
                user_data = {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'role': user.role,
                    'roleDisplay': user.get_role_display(),
                }

                # 添加区域信息
                if user.region:
                    user_data.update({
                        'regionId': user.region.id,
                        'regionName': user.region.name,
                        'regionCode': user.region.code,
                    })
                else:
                    # 超级管理员没有区域限制
                    user_data.update({
                        'regionId': None,
                        'regionName': '全局管理',
                        'regionCode': 'SUPER',
                    })

                return Response({
                    'code': 200,
                    'msg': '登录成功',
                    'data': {
                        'token': f'Bearer {str(refresh.access_token)}',
                        'refreshToken': str(refresh),
                        'userInfo': user_data
                    }
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'code': 401,
                    'msg': '用户名或密码错误',
                    'data': None
                }, status=status.HTTP_401_UNAUTHORIZED)
        except CustomUser.DoesNotExist:
            return Response({
                'code': 401,
                'msg': '用户名或密码错误',
                'data': None
            }, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            return Response({
                'code': 500,
                'msg': '服务器内部错误',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserInfoAPIView(APIView):
    """
    获取当前用户信息API视图
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取当前登录用户的信息"""
        user = request.user

        # 构建用户信息
        user_data = {
            'userId': user.id,
            'userName': user.username,
            'email': user.email,
            'phone': getattr(user, 'phone', ''),
            'role': user.role,
            'roleDisplay': user.get_role_display(),
            'avatar': '',
            'roles': [user.role.lower()],  # 根据实际角色设置
            'buttons': ['add', 'edit', 'delete'],  # 简化处理
        }

        # 添加区域信息
        if user.region:
            user_data.update({
                'regionId': user.region.id,
                'regionName': user.region.name,
                'regionCode': user.region.code,
            })
        else:
            # 超级管理员没有区域限制
            user_data.update({
                'regionId': None,
                'regionName': '全局管理',
                'regionCode': 'SUPER',
            })

        return Response({
            'code': 200,
            'msg': '获取用户信息成功',
            'data': user_data
        }, status=status.HTTP_200_OK)


class UserListAPIView(APIView):
    """
    获取用户列表API视图
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """获取用户列表 - 支持区域感知"""
        current = int(request.GET.get('current', 1))
        size = int(request.GET.get('size', 10))

        # 计算分页
        start = (current - 1) * size
        end = start + size

        # 根据当前用户角色决定查询范围
        current_user = request.user
        if current_user.role == CustomUser.Role.SUPER_ADMIN:
            # 超级管理员可以看到所有用户
            users = CustomUser.objects.unfiltered().all()[start:end]
            total = CustomUser.objects.unfiltered().count()
        else:
            # 区域管理员只能看到同区域的用户
            users = CustomUser.objects.filter(region=current_user.region)[start:end]
            total = CustomUser.objects.filter(region=current_user.region).count()

        user_list = []
        for user in users:
            user_data = {
                'id': user.id,
                'avatar': '',
                'createBy': 'system',
                'createTime': user.date_joined.strftime('%Y-%m-%d %H:%M:%S'),
                'updateBy': 'system',
                'updateTime': user.date_joined.strftime('%Y-%m-%d %H:%M:%S'),
                'status': '1',  # 在线状态
                'userName': user.username,
                'userGender': '未知',
                'nickName': user.username,
                'userPhone': getattr(user, 'phone', ''),
                'userEmail': user.email,
                'userRoles': [user.role.lower()],
                'role': user.role,
                'roleDisplay': user.get_role_display(),
            }

            # 添加区域信息
            if user.region:
                user_data.update({
                    'regionId': user.region.id,
                    'regionName': user.region.name,
                    'regionCode': user.region.code,
                })
            else:
                user_data.update({
                    'regionId': None,
                    'regionName': '全局管理',
                    'regionCode': 'SUPER',
                })

            user_list.append(user_data)

        return Response({
            'code': 200,
            'msg': '获取用户列表成功',
            'data': {
                'records': user_list,
                'current': current,
                'size': size,
                'total': total
            }
        }, status=status.HTTP_200_OK)
