#!/usr/bin/env python3
import requests
import json
import time

# 测试用户编辑功能
base_url = 'http://localhost:8000'

def login(username, password):
    """登录获取token"""
    login_data = {
        'userName': username,
        'password': password
    }
    
    response = requests.post(f'{base_url}/api/v1/auth/login/', 
        data=login_data,
        headers={'X-Region-Code': 'MPR'}
    )
    
    if response.status_code == 200:
        return response.json()['data']['token']
    else:
        raise Exception(f"登录失败: {response.text}")

def get_user_list(token):
    """获取用户列表"""
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR'
    }
    
    response = requests.get(f'{base_url}/api/v1/user/list/', headers=headers)
    if response.status_code == 200:
        return response.json()['data']['records']
    else:
        raise Exception(f"获取用户列表失败: {response.text}")

def get_user_detail(token, user_id):
    """获取用户详情"""
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR'
    }
    
    response = requests.get(f'{base_url}/api/v1/user/{user_id}/', headers=headers)
    if response.status_code == 200:
        return response.json()['data']
    else:
        raise Exception(f"获取用户详情失败: {response.text}")

def update_user(token, user_id, update_data):
    """更新用户"""
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR',
        'Content-Type': 'application/json'
    }
    
    response = requests.put(f'{base_url}/api/v1/user/{user_id}/', 
        json=update_data,
        headers=headers
    )
    
    if response.status_code == 200:
        return response.json()['data']
    else:
        raise Exception(f"更新用户失败: {response.text}")

def test_user_edit_workflow(token):
    """测试完整的用户编辑工作流"""
    print("🔍 获取用户列表...")
    users = get_user_list(token)
    
    # 找一个非超级管理员用户进行测试
    test_user = None
    for user in users:
        if user.get('role') != 'SUPER_ADMIN':
            test_user = user
            break
    
    if not test_user:
        return {'success': False, 'error': '没有找到可测试的用户'}
    
    user_id = test_user['id']
    print(f"📋 选择测试用户: {test_user['userName']} (ID: {user_id})")
    
    # 1. 获取用户详情
    print("📖 获取用户详情...")
    user_detail = get_user_detail(token, user_id)
    
    print(f"  用户名: {user_detail['username']}")
    print(f"  角色: {user_detail['roleDisplay']}")
    print(f"  当前权限数: {len(user_detail['permissions'])}")
    print(f"  负责会员数: {len(user_detail['responsibleMembers'])}")
    
    # 2. 获取权限列表用于更新
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR'
    }
    perm_response = requests.get(f'{base_url}/api/v1/user/permissions/', headers=headers)
    all_permissions = perm_response.json()['data']['permissions']
    
    # 选择不同的权限进行更新
    current_permission_ids = [p['id'] for p in user_detail['permissions']]
    available_permissions = [p for p in all_permissions if p['id'] not in current_permission_ids]
    
    # 选择前3个新权限
    new_permission_ids = current_permission_ids + [p['id'] for p in available_permissions[:3]]
    
    # 3. 更新用户权限
    print(f"🔄 更新用户权限 (从 {len(current_permission_ids)} 个增加到 {len(new_permission_ids)} 个)...")
    
    update_data = {
        'username': user_detail['username'],
        'email': user_detail['email'],
        'phone': user_detail['phone'],
        'role': user_detail['role'],
        'permission_ids': new_permission_ids
    }
    
    # 如果是助理，保持会员关联
    if user_detail['role'] == 'MEMBER_ASSISTANT':
        update_data['responsible_member_ids'] = [m['id'] for m in user_detail['responsibleMembers']]
    
    update_result = update_user(token, user_id, update_data)
    
    # 4. 验证更新结果
    print("✅ 验证更新结果...")
    updated_detail = get_user_detail(token, user_id)
    
    return {
        'success': True,
        'test_user': {
            'id': user_id,
            'username': user_detail['username'],
            'role': user_detail['roleDisplay']
        },
        'before_update': {
            'permissions_count': len(user_detail['permissions']),
            'members_count': len(user_detail['responsibleMembers'])
        },
        'after_update': {
            'permissions_count': len(updated_detail['permissions']),
            'members_count': len(updated_detail['responsibleMembers']),
            'permissions_assigned': update_result.get('permissions_assigned', 0),
            'members_assigned': update_result.get('members_assigned', 0)
        },
        'update_successful': len(updated_detail['permissions']) == len(new_permission_ids)
    }

def test_role_change_workflow(token):
    """测试角色变更工作流"""
    print("\n🔄 测试角色变更功能...")
    
    # 创建一个测试用户
    timestamp = int(time.time())
    create_data = {
        'username': f'role_test_user_{timestamp}',
        'email': f'role_test_{timestamp}@example.com',
        'password': 'password123',
        'role': 'WAREHOUSE_MANAGER',
        'permission_ids': [1, 2, 3]  # 给一些初始权限
    }
    
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR',
        'Content-Type': 'application/json'
    }
    
    # 创建用户
    create_response = requests.post(f'{base_url}/api/v1/user/list/', 
        json=create_data,
        headers=headers
    )
    
    if create_response.status_code != 201:
        return {'success': False, 'error': '创建测试用户失败'}
    
    user_id = create_response.json()['data']['id']
    print(f"✅ 创建测试用户成功 (ID: {user_id})")
    
    # 获取初始状态
    initial_detail = get_user_detail(token, user_id)
    print(f"  初始角色: {initial_detail['roleDisplay']}")
    print(f"  初始权限数: {len(initial_detail['permissions'])}")
    
    # 变更角色为助理
    print("🔄 变更角色为会员助理...")
    
    # 获取会员列表
    member_response = requests.get(f'{base_url}/api/v1/user/members/', headers=headers)
    members = member_response.json()['data']
    selected_members = [m['id'] for m in members[:2]]  # 选择前2个会员
    
    role_change_data = {
        'username': initial_detail['username'],
        'email': initial_detail['email'],
        'phone': initial_detail['phone'],
        'role': 'MEMBER_ASSISTANT',
        'permission_ids': [4, 5, 6],  # 给不同的权限
        'responsible_member_ids': selected_members
    }
    
    update_result = update_user(token, user_id, role_change_data)
    
    # 验证角色变更结果
    final_detail = get_user_detail(token, user_id)
    
    return {
        'success': True,
        'user_id': user_id,
        'role_change': {
            'from': initial_detail['roleDisplay'],
            'to': final_detail['roleDisplay']
        },
        'permissions_change': {
            'from': len(initial_detail['permissions']),
            'to': len(final_detail['permissions'])
        },
        'members_assigned': len(final_detail['responsibleMembers']),
        'role_change_successful': final_detail['role'] == 'MEMBER_ASSISTANT'
    }

def main():
    print("🧪 测试用户编辑功能")
    print("=" * 60)
    
    try:
        # 登录
        print("🔐 登录系统...")
        token = login('mpr_admin', 'admin123')
        print("✅ 登录成功!")
        
        # 测试用户编辑工作流
        print("\n📝 测试用户编辑工作流...")
        edit_result = test_user_edit_workflow(token)
        
        if edit_result['success']:
            print("✅ 用户编辑测试成功!")
            print(f"  测试用户: {edit_result['test_user']['username']} ({edit_result['test_user']['role']})")
            print(f"  权限更新: {edit_result['before_update']['permissions_count']} → {edit_result['after_update']['permissions_count']}")
            print(f"  更新验证: {'✅' if edit_result['update_successful'] else '❌'}")
        else:
            print(f"❌ 用户编辑测试失败: {edit_result['error']}")
        
        # 测试角色变更工作流
        role_result = test_role_change_workflow(token)
        
        if role_result['success']:
            print("✅ 角色变更测试成功!")
            print(f"  角色变更: {role_result['role_change']['from']} → {role_result['role_change']['to']}")
            print(f"  权限变更: {role_result['permissions_change']['from']} → {role_result['permissions_change']['to']}")
            print(f"  会员分配: {role_result['members_assigned']} 个")
            print(f"  变更验证: {'✅' if role_result['role_change_successful'] else '❌'}")
        else:
            print(f"❌ 角色变更测试失败: {role_result.get('error', '未知错误')}")
        
        print("\n🎉 用户编辑功能测试完成!")
        print("\n📋 测试总结:")
        print(f"  - 用户编辑测试: {'✅' if edit_result['success'] else '❌'}")
        print(f"  - 角色变更测试: {'✅' if role_result['success'] else '❌'}")
        
        print(f"\n🌐 前端测试地址: http://localhost:3010/#/system/user")
        print("📝 前端测试步骤:")
        print("  1. 点击用户列表中的'编辑'按钮")
        print("  2. 查看权限选择界面是否显示当前用户权限")
        print("  3. 修改权限选择并保存")
        print("  4. 测试角色变更时的界面变化")
        print("  5. 验证助理角色的会员选择功能")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
