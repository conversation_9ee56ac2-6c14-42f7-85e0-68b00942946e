# core/domain_config.py
"""
多域名配置文件
用于管理不同子域名到区域的映射关系
"""

# 域名到区域代码的映射配置
DOMAIN_REGION_MAPPING = {
    # 子域名前缀 -> 区域代码
    'mpr.': 'MPR',      # mpr.xxxxx.com -> MPR区域
    'rl.': 'RL',        # rl.xxxxx.com -> RL区域  
    'eo.': 'EO',        # eo.xxxxx.com -> EO区域
    'zz.': 'ZZ',        # zz.xxxxx.com -> ZZ区域（郑州）
    'wh.': 'WH',        # wh.xxxxx.com -> WH区域（武汉）
}

# 支持的完整域名列表（用于开发和测试）
SUPPORTED_DOMAINS = [
    'mpr.dawn-erp.com',
    'rl.dawn-erp.com', 
    'eo.dawn-erp.com',
    'zz.dawn-erp.com',
    'wh.dawn-erp.com',
    'localhost:3000',      # 开发环境
    '127.0.0.1:3000',      # 本地测试
    'localhost:8080',      # 前端开发服务器
    '127.0.0.1:8080',      # 前端开发服务器
]

# 默认域名（当无法识别域名时使用）
DEFAULT_DOMAIN = 'localhost'

def get_region_code_from_domain(host):
    """
    从域名中提取区域代码
    
    Args:
        host (str): 请求的主机名
        
    Returns:
        str or None: 区域代码，如果无法识别则返回None
    """
    host = host.lower()
    
    # 检查子域名映射
    for subdomain_prefix, region_code in DOMAIN_REGION_MAPPING.items():
        if host.startswith(subdomain_prefix):
            return region_code
    
    # 检查完整域名映射
    for domain in SUPPORTED_DOMAINS:
        if host == domain.lower():
            # 从完整域名中提取子域名前缀
            if '.' in domain and not domain.startswith('localhost') and not domain.startswith('127.0.0.1'):
                subdomain = domain.split('.')[0] + '.'
                return DOMAIN_REGION_MAPPING.get(subdomain)
    
    return None

def is_supported_domain(host):
    """
    检查域名是否被支持
    
    Args:
        host (str): 请求的主机名
        
    Returns:
        bool: 是否为支持的域名
    """
    host = host.lower()
    
    # 检查是否在支持的域名列表中
    if host in [d.lower() for d in SUPPORTED_DOMAINS]:
        return True
    
    # 检查是否匹配子域名前缀
    for subdomain_prefix in DOMAIN_REGION_MAPPING.keys():
        if host.startswith(subdomain_prefix):
            return True
    
    return False

def get_domain_info(host):
    """
    获取域名的详细信息
    
    Args:
        host (str): 请求的主机名
        
    Returns:
        dict: 包含域名信息的字典
    """
    region_code = get_region_code_from_domain(host)
    is_supported = is_supported_domain(host)
    
    return {
        'host': host,
        'region_code': region_code,
        'is_supported': is_supported,
        'is_multi_region': region_code is not None,
        'access_type': 'region' if region_code else 'global'
    }
