{"name": "art-design-pro", "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --open", "build": "vue-tsc --noEmit && vite build", "serve": "vite preview", "lint": "eslint", "fix": "eslint --fix", "lint:prettier": "prettier --write \"**/*.{js,cjs,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint  \"**/*.{css,scss,vue}\" --fix", "lint:lint-staged": "lint-staged", "prepare": "husky", "commit": "git-cz"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "lint-staged": {"*.{js,ts,mjs,mts,tsx}": ["eslint --fix", "prettier --write"], "*.{cjs,json,jsonc}": ["prettier --write"], "*.vue": ["eslint --fix", "stylelint --fix --allow-empty-input", "prettier --write"], "*.{html,htm}": ["prettier --write"], "*.{scss,css,less}": ["stylelint --fix --allow-empty-input", "prettier --write"], "*.{md,mdx}": ["prettier --write"], "*.{yaml,yml}": ["prettier --write"]}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vue/reactivity": "^3.4.35", "@vueuse/core": "^11.0.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "next", "axios": "^1.7.5", "crypto-js": "^4.2.0", "echarts": "^5.6.0", "element-plus": "^2.8.0", "file-saver": "^2.0.5", "highlight.js": "^11.10.0", "md-editor-v3": "^4.17.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.3.0", "qrcode.vue": "^3.6.0", "vue": "^3.5.12", "vue-draggable-plus": "^0.6.0", "vue-i18n": "^9.14.0", "vue-router": "^4.4.2", "xgplayer": "^3.0.20", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^19.4.1", "@commitlint/config-conventional": "^19.4.1", "@eslint/js": "^9.9.1", "@types/node": "^22.1.0", "@typescript-eslint/eslint-plugin": "^8.3.0", "@typescript-eslint/parser": "^8.3.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/compiler-sfc": "^3.0.5", "commitizen": "^4.3.0", "cz-git": "^1.11.1", "eslint": "^9.9.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.27.0", "globals": "^15.9.0", "husky": "^9.1.5", "lint-staged": "^15.5.2", "prettier": "^3.5.3", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.81.0", "stylelint": "^16.20.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.1", "terser": "^5.36.0", "typescript": "~5.6.3", "typescript-eslint": "^8.9.0", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "vite": "^6.1.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-vue-devtools": "^7.7.6", "vue-demi": "^0.14.9", "vue-img-cutter": "^3.0.5", "vue-tsc": "~2.1.6"}}