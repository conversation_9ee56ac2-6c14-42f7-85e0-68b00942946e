// ANSI 转义码生成网站  https://patorjk.com/software/taag/#p=display&f=Big&t=ABB%0A
const asciiArt = `
\x1b[32m✨ 你好！欢迎使用 Art Design Pro！
\x1b[0m
\x1b[36m💡 如果您觉得项目对您有帮助，请点击下面的链接为我点个 ★Star 支持一下！祝您使用愉快！
\x1b[0m
\x1b[33m🌟 GitHub: https://github.com/Daymychen/art-design-pro
\x1b[0m
\x1b[31m✨ 技术支持（QQ群）: 821834289，如果你有任何问题，请加入QQ群，我们会在第一时间为你解答！
\x1b[0m

\x1b[36m      _       _______   _________   ______   ________   ______   _____   ______  ____  _____   _______  _______      ___    
\x1b[36m     / \\     |_   __ \\ |  _   _  | |_   _ \`.|_   __  |.' ____ \\ |_   _|.' ___  ||_   \\|_   _| |_   __ \\|_   __ \\   .'   \`.  
\x1b[36m    / _ \\      | |__) ||_/ | | \\_|   | | \`. \\ | |_ \\_|| (___ \\_|  | | / .'   \\_|  |   \\ | |     | |__) | | |__) | /  .-.  \\ 
\x1b[36m   / ___ \\     |  __ /     | |       | |  | | |  _| _  _.____\`.   | | | |   ____  | |\\ \\| |     |  ___/  |  __ /  | |   | | 
\x1b[36m _/ /   \\ \\_  _| |  \\ \\_  _| |_     _| |_.' /_| |__/ || \\____) | _| |_\\ \`.___]  |_| |_\\   |_   _| |_    _| |  \\ \\_\\  \`-'  / 
\x1b[36m|____| |____||____| |___||_____|   |______.'|________| \\______.'|_____|\\\`._____.'|_____|\\____| |_____|  |____| |___|\\\`.___.'  
\x1b[0m
`

console.log(asciiArt)
