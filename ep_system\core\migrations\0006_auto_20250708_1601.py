# Generated by Django 5.2.3 on 2025-07-08 08:01

from django.db import migrations
from django.db import models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0005_permission_userpermission'),
    ]

    operations = [
        # 确保权限表在主数据库中创建
        migrations.RunSQL(
            "CREATE TABLE IF NOT EXISTS core_permission LIKE core_permission;",
            reverse_sql="DROP TABLE IF EXISTS core_permission;",
            state_operations=[
                migrations.CreateModel(
                    name='Permission',
                    fields=[
                        ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                        ('name', models.CharField(max_length=100, unique=True, verbose_name='权限名称')),
                        ('code', models.CharField(max_length=50, unique=True, verbose_name='权限代码')),
                        ('description', models.TextField(blank=True, verbose_name='权限描述')),
                        ('category', models.CharField(max_length=50, verbose_name='权限分类')),
                    ],
                    options={
                        'verbose_name': '权限',
                        'verbose_name_plural': '权限',
                    },
                ),
            ]
        ),
        migrations.RunSQL(
            "CREATE TABLE IF NOT EXISTS core_userpermission LIKE core_userpermission;",
            reverse_sql="DROP TABLE IF EXISTS core_userpermission;",
            state_operations=[
                migrations.CreateModel(
                    name='UserPermission',
                    fields=[
                        ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                        ('granted_at', models.DateTimeField(auto_now_add=True, verbose_name='授权时间')),
                        ('granted_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='granted_permissions', to='core.customuser', verbose_name='授权人')),
                        ('permission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.permission')),
                        ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='permission_grants', to='core.customuser')),
                    ],
                    options={
                        'verbose_name': '用户权限',
                        'verbose_name_plural': '用户权限',
                    },
                ),
            ]
        ),
    ]
