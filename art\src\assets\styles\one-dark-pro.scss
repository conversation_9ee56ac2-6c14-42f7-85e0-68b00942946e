/*
Atom One Dark by <PERSON>
Original One Dark Syntax theme from https://github.com/atom/one-dark-syntax
base:    #282c34
mono-1:  #abb2bf
mono-2:  #818896
mono-3:  #5c6370
hue-1:   #56b6c2
hue-2:   #61aeee
hue-3:   #c678dd
hue-4:   #98c379
hue-5:   #e06c75
hue-5-2: #be5046
hue-6:   #d19a66
hue-6-2: #e6c07b
*/

.hljs {
  display: block;
  overflow-x: auto;
  padding: 0.5em;
  // color: #abb2bf;
  // background: #282c34;

  color: #a6accd;
}

.hljs-string,
.hljs-section,
.hljs-selector-class,
.hljs-template-variable,
.hljs-deletion {
  color: #aed07e !important;
}

.hljs-comment,
.hljs-quote {
  color: #6f747d;
}

.hljs-doctag,
.hljs-keyword,
.hljs-formula {
  color: #c792ea;
}

.hljs-section,
.hljs-name,
.hljs-selector-tag,
.hljs-deletion,
.hljs-subst {
  color: #c86068;
}

.hljs-literal {
  color: #56b6c2;
}

.hljs-string,
.hljs-regexp,
.hljs-addition,
.hljs-attribute,
.hljs-meta-string {
  color: #abb2bf;
}

.hljs-attribute {
  color: #c792ea;
}

.hljs-function {
  color: #c792ea;
}

.hljs-type {
  color: #f07178;
}

.hljs-title {
  color: #82aaff !important;
}

.hljs-built_in,
.hljs-class {
  color: #82aaff;
}

// 括号
.hljs-params {
  color: #a6accd;
}

.hljs-attr,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-class,
.hljs-selector-attr,
.hljs-selector-pseudo,
.hljs-number {
  color: #de7e61;
}

.hljs-symbol,
.hljs-bullet,
.hljs-link,
.hljs-meta,
.hljs-selector-id {
  color: #61aeee;
}

.hljs-strong {
  font-weight: bold;
}

.hljs-link {
  text-decoration: underline;
}
