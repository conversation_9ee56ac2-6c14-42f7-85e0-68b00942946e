#!/usr/bin/env python3
import requests
import json

def test_login_and_database():
    """测试登录和数据库API"""
    
    # 测试登录
    print("🔐 测试登录API...")
    response = requests.post('http://localhost:8000/api/v1/auth/login/',
        headers={'Content-Type': 'application/json', 'X-Region-Code': 'MPR'},
        json={'userName': 'mpr_admin', 'password': 'admin123'}
    )

    print(f'登录状态码: {response.status_code}')
    if response.status_code == 200:
        data = response.json()
        print('✅ 登录成功!')
        # 从data字段中获取token和用户信息
        response_data = data.get('data', {})
        token = response_data.get('token', '')
        print(f'Token: {token[:50]}...')
        user_info = response_data.get('userInfo', {})
        print(f'用户: {user_info.get("username")} ({user_info.get("role")})')
        print(f'区域: {user_info.get("regionName", "无")}')
        
        # 测试数据库状态API
        print('\n📊 测试数据库状态API...')
        db_response = requests.get('http://localhost:8000/api/v1/business/database/status/',
            headers={'Authorization': token, 'X-Region-Code': 'MPR'}
        )
        print(f'数据库状态API状态码: {db_response.status_code}')
        if db_response.status_code == 200:
            db_data = db_response.json()
            print('✅ 数据库状态API成功!')
            print(f'当前区域: {db_data.get("current_region", {})}')
            print(f'当前数据库: {db_data.get("current_database")}')
            print(f'数据库总数: {db_data.get("total_databases")}')
            
            # 显示所有数据库状态
            databases = db_data.get('databases', {})
            print('\n数据库连接状态:')
            for alias, info in databases.items():
                status = "✅" if info.get('status') == 'connected' else "❌"
                print(f'  {status} {alias}: {info.get("database_name")} - {info.get("status")}')
        else:
            print(f'❌ 数据库状态API失败: {db_response.text}')
            
        # 测试创建测试数据API
        print('\n🔧 测试创建测试数据API...')
        test_data_response = requests.post('http://localhost:8000/api/v1/business/test-data/create/',
            headers={'Authorization': token, 'X-Region-Code': 'MPR'}
        )
        print(f'创建测试数据API状态码: {test_data_response.status_code}')
        if test_data_response.status_code == 200:
            test_data = test_data_response.json()
            print('✅ 创建测试数据成功!')
            print(f'区域: {test_data.get("region")}')
            print(f'数据库: {test_data.get("database")}')
            created_data = test_data.get('created_data', {})
            print(f'创建的客户: {created_data.get("customer", {}).get("name")}')
            print(f'创建的产品: {created_data.get("product", {}).get("name")}')
            print(f'创建的订单: {created_data.get("order", {}).get("number")}')
        else:
            print(f'❌ 创建测试数据失败: {test_data_response.text}')
            
    else:
        print(f'❌ 登录失败: {response.text}')

if __name__ == '__main__':
    test_login_and_database()
