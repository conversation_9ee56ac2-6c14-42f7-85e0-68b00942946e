#!/usr/bin/env python3
import requests
import json
import time

# 测试前端集成功能
base_url = 'http://localhost:8000'

def login(username, password):
    """登录获取token"""
    login_data = {
        'userName': username,
        'password': password
    }
    
    response = requests.post(f'{base_url}/api/v1/auth/login/', 
        data=login_data,
        headers={'X-Region-Code': 'MPR'}
    )
    
    if response.status_code == 200:
        return response.json()['data']['token']
    else:
        raise Exception(f"登录失败: {response.text}")

def test_api_endpoints(token):
    """测试所有API端点"""
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR'
    }
    
    endpoints = [
        ('/api/v1/user/permissions/', '权限列表'),
        ('/api/v1/user/members/', '会员列表'),
        ('/api/v1/user/assistants/', '助理列表'),
        ('/api/v1/user/list/', '用户列表')
    ]
    
    results = {}
    
    for endpoint, name in endpoints:
        try:
            response = requests.get(f'{base_url}{endpoint}', headers=headers)
            results[name] = {
                'status': response.status_code,
                'success': response.status_code == 200,
                'data_count': len(response.json().get('data', [])) if response.status_code == 200 else 0
            }
            if response.status_code == 200 and endpoint == '/api/v1/user/permissions/':
                # 特殊处理权限API
                data = response.json()['data']
                results[name]['permissions_count'] = len(data.get('permissions', []))
                results[name]['categories_count'] = len(data.get('categories', {}))
        except Exception as e:
            results[name] = {
                'status': 'error',
                'success': False,
                'error': str(e)
            }
    
    return results

def test_user_creation_with_permissions(token):
    """测试带权限的用户创建"""
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR',
        'Content-Type': 'application/json'
    }
    
    # 先获取权限列表
    perm_response = requests.get(f'{base_url}/api/v1/user/permissions/', headers=headers)
    if perm_response.status_code != 200:
        return {'success': False, 'error': '无法获取权限列表'}
    
    permissions = perm_response.json()['data']['permissions']
    selected_permissions = [p['id'] for p in permissions[:5]]  # 选择前5个权限
    
    # 创建用户
    timestamp = int(time.time())
    user_data = {
        'username': f'frontend_test_user_{timestamp}',
        'email': f'frontend_test_{timestamp}@example.com',
        'password': 'password123',
        'role': 'WAREHOUSE_MANAGER',
        'permission_ids': selected_permissions
    }
    
    response = requests.post(f'{base_url}/api/v1/user/list/', 
        json=user_data,
        headers=headers
    )
    
    return {
        'success': response.status_code == 201,
        'status': response.status_code,
        'data': response.json() if response.status_code == 201 else None,
        'error': response.text if response.status_code != 201 else None
    }

def test_assistant_creation_with_members(token):
    """测试助理创建并关联会员"""
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR',
        'Content-Type': 'application/json'
    }
    
    # 获取会员列表
    member_response = requests.get(f'{base_url}/api/v1/user/members/', headers=headers)
    if member_response.status_code != 200:
        return {'success': False, 'error': '无法获取会员列表'}
    
    members = member_response.json()['data']
    if len(members) < 2:
        return {'success': False, 'error': '会员数量不足，无法测试助理关联'}
    
    # 获取权限列表
    perm_response = requests.get(f'{base_url}/api/v1/user/permissions/', headers=headers)
    permissions = perm_response.json()['data']['permissions']
    selected_permissions = [p['id'] for p in permissions[:3]]  # 选择前3个权限
    
    # 选择前2个会员
    selected_members = [m['id'] for m in members[:2]]
    
    # 创建助理
    timestamp = int(time.time())
    assistant_data = {
        'username': f'frontend_assistant_{timestamp}',
        'email': f'frontend_assistant_{timestamp}@example.com',
        'password': 'password123',
        'role': 'MEMBER_ASSISTANT',
        'permission_ids': selected_permissions,
        'responsible_member_ids': selected_members
    }
    
    response = requests.post(f'{base_url}/api/v1/user/list/', 
        json=assistant_data,
        headers=headers
    )
    
    return {
        'success': response.status_code == 201,
        'status': response.status_code,
        'data': response.json() if response.status_code == 201 else None,
        'error': response.text if response.status_code != 201 else None,
        'selected_members': len(selected_members),
        'selected_permissions': len(selected_permissions)
    }

def main():
    print("🧪 测试前端集成功能")
    print("=" * 60)
    
    try:
        # 1. 登录
        print("🔐 登录系统...")
        token = login('mpr_admin', 'admin123')
        print("✅ 登录成功!")
        
        # 2. 测试所有API端点
        print("\n📡 测试API端点...")
        api_results = test_api_endpoints(token)
        
        for name, result in api_results.items():
            if result['success']:
                if name == '权限列表':
                    print(f"✅ {name}: {result['permissions_count']} 个权限，{result['categories_count']} 个分类")
                else:
                    print(f"✅ {name}: {result['data_count']} 条记录")
            else:
                print(f"❌ {name}: 状态码 {result['status']}")
        
        # 3. 测试用户创建（带权限）
        print("\n👤 测试用户创建（带权限分配）...")
        user_result = test_user_creation_with_permissions(token)
        
        if user_result['success']:
            data = user_result['data']['data']
            print("✅ 用户创建成功!")
            print(f"  用户名: {data['username']}")
            print(f"  角色: {data['roleDisplay']}")
            print(f"  分配权限数: {data['permissions_assigned']}")
        else:
            print(f"❌ 用户创建失败: {user_result['error']}")
        
        # 4. 测试助理创建（带会员关联）
        print("\n🤝 测试助理创建（带会员关联）...")
        assistant_result = test_assistant_creation_with_members(token)
        
        if assistant_result['success']:
            data = assistant_result['data']['data']
            print("✅ 助理创建成功!")
            print(f"  助理用户名: {data['username']}")
            print(f"  角色: {data['roleDisplay']}")
            print(f"  分配权限数: {data['permissions_assigned']}")
            print(f"  负责会员数: {data['members_assigned']}")
        else:
            print(f"❌ 助理创建失败: {assistant_result['error']}")
        
        print("\n🎉 前端集成测试完成!")
        print("\n📋 测试总结:")
        print(f"  - API端点测试: {sum(1 for r in api_results.values() if r['success'])}/{len(api_results)} 通过")
        print(f"  - 用户创建测试: {'✅' if user_result['success'] else '❌'}")
        print(f"  - 助理创建测试: {'✅' if assistant_result['success'] else '❌'}")
        
        # 5. 前端访问提示
        print(f"\n🌐 前端访问地址: http://localhost:3010")
        print("📝 测试步骤:")
        print("  1. 访问 http://localhost:3010/#/system/user")
        print("  2. 点击'新增用户'按钮")
        print("  3. 选择不同角色查看权限选择界面")
        print("  4. 选择'会员助理'角色查看会员选择界面")
        print("  5. 测试权限多选功能")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
