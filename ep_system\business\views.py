# business/views.py
"""
业务数据API视图
演示分库架构的功能
"""

from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.db import connections
from django.conf import settings
from core.middleware import get_current_region
from core.database_router import DatabaseManager
from .models import Customer, Product, Order, Inventory
import random
import string


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def database_status(request):
    """
    获取数据库状态信息
    """
    try:
        current_region = get_current_region()
        current_db = DatabaseManager.get_current_db()
        
        # 获取所有数据库连接状态
        db_status = {}
        for db_alias in settings.DATABASES.keys():
            try:
                connection = connections[db_alias]
                with connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    db_status[db_alias] = {
                        'status': 'connected',
                        'database_name': settings.DATABASES[db_alias]['NAME']
                    }
            except Exception as e:
                db_status[db_alias] = {
                    'status': 'error',
                    'error': str(e),
                    'database_name': settings.DATABASES[db_alias]['NAME']
                }
        
        return Response({
            'current_region': {
                'code': current_region.code if current_region else None,
                'name': current_region.name if current_region else None,
            },
            'current_database': current_db,
            'databases': db_status,
            'total_databases': len(settings.DATABASES)
        })
        
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_test_data(request):
    """
    在当前区域数据库中创建测试数据
    """
    try:
        current_region = get_current_region()
        if not current_region:
            return Response({
                'error': '无法确定当前区域'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        current_db = DatabaseManager.get_current_db()
        
        # 生成随机数据
        def random_string(length=8):
            return ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))
        
        # 创建客户
        customer = Customer.objects.create(
            customer_code=f"CUST_{current_region.code}_{random_string(6)}",
            name=f"{current_region.name}测试客户_{random_string(4)}",
            contact_person=f"联系人_{random_string(3)}",
            phone=f"138{random.randint(10000000, 99999999)}",
            email=f"test_{random_string(4).lower()}@example.com",
            address=f"{current_region.name}市测试地址{random.randint(1, 999)}号",
            created_by_id=request.user.id
        )
        
        # 创建产品
        product = Product.objects.create(
            product_code=f"PROD_{current_region.code}_{random_string(6)}",
            name=f"{current_region.name}测试产品_{random_string(4)}",
            description=f"这是{current_region.name}区域的测试产品",
            category="测试类别",
            unit="个",
            price=random.randint(100, 10000) / 100,
            cost=random.randint(50, 5000) / 100,
            created_by_id=request.user.id
        )
        
        # 创建订单
        order = Order.objects.create(
            order_number=f"ORD_{current_region.code}_{random_string(8)}",
            customer=customer,
            status=Order.OrderStatus.PENDING,
            total_amount=product.price * 2,
            notes=f"来自{current_region.name}区域的测试订单",
            created_by_id=request.user.id
        )
        
        # 创建库存
        inventory = Inventory.objects.create(
            product=product,
            warehouse_location=f"{current_region.name}仓库A区",
            quantity_on_hand=random.randint(100, 1000),
            quantity_reserved=random.randint(0, 50),
            reorder_point=random.randint(10, 100),
            created_by_id=request.user.id
        )
        
        return Response({
            'message': f'测试数据创建成功！',
            'region': current_region.name,
            'database': current_db,
            'created_data': {
                'customer': {
                    'id': customer.id,
                    'code': customer.customer_code,
                    'name': customer.name
                },
                'product': {
                    'id': product.id,
                    'code': product.product_code,
                    'name': product.name
                },
                'order': {
                    'id': order.id,
                    'number': order.order_number,
                    'total': str(order.total_amount)
                },
                'inventory': {
                    'id': inventory.id,
                    'location': inventory.warehouse_location,
                    'quantity': str(inventory.quantity_on_hand)
                }
            }
        })
        
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_region_data(request):
    """
    列出当前区域的数据
    """
    try:
        current_region = get_current_region()
        current_db = DatabaseManager.get_current_db()
        
        # 获取各种数据的数量
        customers_count = Customer.objects.count()
        products_count = Product.objects.count()
        orders_count = Order.objects.count()
        inventory_count = Inventory.objects.count()
        
        # 获取最近的数据
        recent_customers = Customer.objects.order_by('-created_at')[:5]
        recent_products = Product.objects.order_by('-created_at')[:5]
        recent_orders = Order.objects.order_by('-created_at')[:5]
        
        return Response({
            'region': {
                'code': current_region.code if current_region else None,
                'name': current_region.name if current_region else None,
            },
            'database': current_db,
            'statistics': {
                'customers': customers_count,
                'products': products_count,
                'orders': orders_count,
                'inventory': inventory_count
            },
            'recent_data': {
                'customers': [
                    {
                        'id': c.id,
                        'code': c.customer_code,
                        'name': c.name,
                        'created_at': c.created_at
                    } for c in recent_customers
                ],
                'products': [
                    {
                        'id': p.id,
                        'code': p.product_code,
                        'name': p.name,
                        'price': str(p.price),
                        'created_at': p.created_at
                    } for p in recent_products
                ],
                'orders': [
                    {
                        'id': o.id,
                        'number': o.order_number,
                        'customer': o.customer.name,
                        'total': str(o.total_amount),
                        'status': o.status,
                        'created_at': o.created_at
                    } for o in recent_orders
                ]
            }
        })
        
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
