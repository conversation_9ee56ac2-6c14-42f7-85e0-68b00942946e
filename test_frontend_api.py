#!/usr/bin/env python3
import requests
import json

# 测试前端API调用
base_url = 'http://localhost:8000'

# 1. 登录获取token
login_data = {
    'userName': 'mpr_admin',  # 注意是userName不是username
    'password': 'admin123'
}

print("🔐 测试登录...")
response = requests.post(f'{base_url}/api/v1/auth/login/',
    data=login_data,  # 使用data而不是json
    headers={'X-Region-Code': 'MPR'}
)

if response.status_code == 200:
    data = response.json()
    token = data['data']['token']
    print("✅ 登录成功!")
    
    # 2. 测试获取助理列表API
    print("\n👥 测试获取助理列表...")
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR'
    }
    
    response = requests.get(f'{base_url}/api/v1/user/assistants/', headers=headers)
    print(f"助理列表API状态码: {response.status_code}")
    if response.status_code == 200:
        assistants = response.json()['data']
        print(f"✅ 找到 {len(assistants)} 个助理")
        for assistant in assistants:
            print(f"  - {assistant['username']} ({assistant['email']}) - 负责 {assistant['managedMemberCount']} 个会员")
    else:
        print(f"❌ 获取助理列表失败: {response.text}")
    
    # 3. 测试创建会员并关联助理
    if assistants:
        assistant_id = assistants[0]['id']
        print(f"\n👤 测试创建会员并关联助理 (ID: {assistant_id})...")
        
        import time
        timestamp = int(time.time())
        create_user_data = {
            'username': f'test_frontend_member_{timestamp}',
            'email': f'frontend_member_{timestamp}@example.com',
            'password': 'password123',
            'role': 'MEMBER',
            'managed_by_id': assistant_id
        }
        
        response = requests.post(f'{base_url}/api/v1/user/list/',
            json=create_user_data,
            headers=headers
        )
        
        print(f"创建会员状态码: {response.status_code}")
        if response.status_code == 201:
            print("✅ 会员创建成功!")
            print(f"响应: {response.json()}")
        else:
            print(f"❌ 创建会员失败: {response.text}")
    
    # 4. 测试获取用户列表
    print(f"\n📋 测试获取用户列表...")
    response = requests.get(f'{base_url}/api/v1/user/list/', headers=headers)
    print(f"用户列表API状态码: {response.status_code}")
    if response.status_code == 200:
        users_data = response.json()['data']
        users = users_data.get('records', []) if isinstance(users_data, dict) else users_data
        print(f"✅ 找到 {len(users)} 个用户")

        # 查找我们刚创建的用户
        test_user = None
        for user in users:
            if isinstance(user, dict) and user.get('userName', '').startswith('test_frontend_member_'):
                test_user = user
                break
        
        if test_user:
            print("✅ 找到测试会员!")
            print(f"会员信息: {test_user['userName']} ({test_user['userEmail']})")
            if test_user.get('managedById'):
                print(f"负责助理: {test_user['managedByName']}")
                print(f"助理邮箱: {test_user['managedByEmail']}")
            else:
                print("❌ 未找到助理信息")
        else:
            print("❌ 未找到测试会员")
    else:
        print(f"❌ 获取用户列表失败: {response.text}")

else:
    print(f"❌ 登录失败: {response.text}")
