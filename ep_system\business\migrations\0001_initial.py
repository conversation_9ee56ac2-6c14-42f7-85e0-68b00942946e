# Generated by Django 5.2.3 on 2025-07-08 03:09

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('customer_code', models.CharField(max_length=50, unique=True, verbose_name='客户编码')),
                ('name', models.CharField(max_length=200, verbose_name='客户名称')),
                ('contact_person', models.CharField(blank=True, max_length=100, verbose_name='联系人')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='联系电话')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='邮箱')),
                ('address', models.TextField(blank=True, verbose_name='地址')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='更新人')),
            ],
            options={
                'verbose_name': '客户',
                'verbose_name_plural': '客户',
                'db_table': 'business_customer',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='订单号')),
                ('status', models.CharField(choices=[('PENDING', '待处理'), ('CONFIRMED', '已确认'), ('PROCESSING', '处理中'), ('SHIPPED', '已发货'), ('DELIVERED', '已送达'), ('CANCELLED', '已取消')], default='PENDING', max_length=20, verbose_name='订单状态')),
                ('order_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='订单日期')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='订单总额')),
                ('notes', models.TextField(blank=True, verbose_name='备注')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='business.customer', verbose_name='客户')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='更新人')),
            ],
            options={
                'verbose_name': '订单',
                'verbose_name_plural': '订单',
                'db_table': 'business_order',
                'ordering': ['-order_date'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('product_code', models.CharField(max_length=50, unique=True, verbose_name='产品编码')),
                ('name', models.CharField(max_length=200, verbose_name='产品名称')),
                ('description', models.TextField(blank=True, verbose_name='产品描述')),
                ('category', models.CharField(blank=True, max_length=100, verbose_name='产品类别')),
                ('unit', models.CharField(default='个', max_length=20, verbose_name='单位')),
                ('price', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='单价')),
                ('cost', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='成本')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='更新人')),
            ],
            options={
                'verbose_name': '产品',
                'verbose_name_plural': '产品',
                'db_table': 'business_product',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='数量')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='单价')),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='小计')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='business.order', verbose_name='订单')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='更新人')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='business.product', verbose_name='产品')),
            ],
            options={
                'verbose_name': '订单明细',
                'verbose_name_plural': '订单明细',
                'db_table': 'business_order_item',
            },
        ),
        migrations.CreateModel(
            name='Inventory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('warehouse_location', models.CharField(blank=True, max_length=100, verbose_name='仓库位置')),
                ('quantity_on_hand', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='现有库存')),
                ('quantity_reserved', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='预留库存')),
                ('quantity_available', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='可用库存')),
                ('reorder_point', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='补货点')),
                ('last_updated', models.DateTimeField(auto_now=True, verbose_name='最后更新')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL, verbose_name='更新人')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='business.product', verbose_name='产品')),
            ],
            options={
                'verbose_name': '库存',
                'verbose_name_plural': '库存',
                'db_table': 'business_inventory',
                'unique_together': {('product', 'warehouse_location')},
            },
        ),
    ]
