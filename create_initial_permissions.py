#!/usr/bin/env python3
"""
创建初始权限数据
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.join(os.path.dirname(__file__), 'ep_system'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ep_system.settings')
django.setup()

# from core.models import Permission  # 不使用ORM，直接用SQL

def create_initial_permissions():
    """创建初始权限数据"""
    from django.db import connection

    # 使用主数据库连接
    with connection.cursor() as cursor:
        # 检查权限表是否存在，如果不存在则创建
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS core_permission (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) UNIQUE NOT NULL,
                code VARCHAR(50) UNIQUE NOT NULL,
                description TEXT,
                category VARCHAR(50) NOT NULL
            )
        """)

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS core_userpermission (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                user_id BIGINT NOT NULL,
                permission_id BIGINT NOT NULL,
                granted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                granted_by_id BIGINT,
                UNIQUE KEY unique_user_permission (user_id, permission_id),
                FOREIGN KEY (user_id) REFERENCES core_customuser(id) ON DELETE CASCADE,
                FOREIGN KEY (permission_id) REFERENCES core_permission(id) ON DELETE CASCADE,
                FOREIGN KEY (granted_by_id) REFERENCES core_customuser(id) ON DELETE SET NULL
            )
        """)

    permissions = [
        # 用户管理权限
        {'name': '查看用户列表', 'code': 'user.view', 'category': '用户管理', 'description': '查看用户列表页面'},
        {'name': '创建用户', 'code': 'user.create', 'category': '用户管理', 'description': '创建新用户'},
        {'name': '编辑用户', 'code': 'user.edit', 'category': '用户管理', 'description': '编辑用户信息'},
        {'name': '删除用户', 'code': 'user.delete', 'category': '用户管理', 'description': '删除用户'},
        {'name': '重置密码', 'code': 'user.reset_password', 'category': '用户管理', 'description': '重置用户密码'},
        
        # 系统管理权限
        {'name': '系统设置', 'code': 'system.settings', 'category': '系统管理', 'description': '访问系统设置'},
        {'name': '查看日志', 'code': 'system.logs', 'category': '系统管理', 'description': '查看系统日志'},
        {'name': '数据备份', 'code': 'system.backup', 'category': '系统管理', 'description': '执行数据备份'},
        
        # 业务管理权限
        {'name': '查看订单', 'code': 'order.view', 'category': '业务管理', 'description': '查看订单列表'},
        {'name': '创建订单', 'code': 'order.create', 'category': '业务管理', 'description': '创建新订单'},
        {'name': '编辑订单', 'code': 'order.edit', 'category': '业务管理', 'description': '编辑订单信息'},
        {'name': '删除订单', 'code': 'order.delete', 'category': '业务管理', 'description': '删除订单'},
        
        # 库存管理权限
        {'name': '查看库存', 'code': 'inventory.view', 'category': '库存管理', 'description': '查看库存信息'},
        {'name': '库存入库', 'code': 'inventory.inbound', 'category': '库存管理', 'description': '执行库存入库操作'},
        {'name': '库存出库', 'code': 'inventory.outbound', 'category': '库存管理', 'description': '执行库存出库操作'},
        {'name': '库存盘点', 'code': 'inventory.stocktaking', 'category': '库存管理', 'description': '执行库存盘点'},
        
        # 报表权限
        {'name': '查看销售报表', 'code': 'report.sales', 'category': '报表管理', 'description': '查看销售报表'},
        {'name': '查看库存报表', 'code': 'report.inventory', 'category': '报表管理', 'description': '查看库存报表'},
        {'name': '导出报表', 'code': 'report.export', 'category': '报表管理', 'description': '导出报表数据'},
        
        # 财务权限
        {'name': '查看财务数据', 'code': 'finance.view', 'category': '财务管理', 'description': '查看财务数据'},
        {'name': '财务审核', 'code': 'finance.audit', 'category': '财务管理', 'description': '执行财务审核'},
    ]
    
    created_count = 0
    for perm_data in permissions:
        # 直接使用SQL插入，避免ORM路由问题
        with connection.cursor() as cursor:
            # 检查权限是否已存在
            cursor.execute("SELECT id FROM core_permission WHERE code = %s", [perm_data['code']])
            if cursor.fetchone():
                print(f"⚠️ 权限已存在: {perm_data['name']} ({perm_data['code']})")
            else:
                # 插入新权限
                cursor.execute("""
                    INSERT INTO core_permission (name, code, description, category)
                    VALUES (%s, %s, %s, %s)
                """, [perm_data['name'], perm_data['code'], perm_data['description'], perm_data['category']])
                created_count += 1
                print(f"✅ 创建权限: {perm_data['name']} ({perm_data['code']})")

    # 获取总权限数量
    with connection.cursor() as cursor:
        cursor.execute("SELECT COUNT(*) FROM core_permission")
        total_count = cursor.fetchone()[0]

    print(f"\n🎉 权限初始化完成！共创建 {created_count} 个新权限")
    print(f"📊 总权限数量: {total_count}")

if __name__ == "__main__":
    create_initial_permissions()
