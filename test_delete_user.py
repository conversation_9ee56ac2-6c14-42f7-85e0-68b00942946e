#!/usr/bin/env python3
"""
测试用户删除功能
"""
import requests
import json

base_url = 'http://localhost:8000'

def login(username, password):
    """登录并返回token"""
    login_data = {
        'userName': username,
        'password': password
    }
    
    response = requests.post(f'{base_url}/api/v1/auth/login/', 
        data=login_data,
        headers={'X-Region-Code': 'MPR'}
    )
    
    if response.status_code == 200:
        return response.json()['data']['token']
    else:
        raise Exception(f"登录失败: {response.text}")

def get_user_list(token):
    """获取用户列表"""
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR'
    }
    
    response = requests.get(f'{base_url}/api/v1/user/list/?current=1&size=50', headers=headers)
    if response.status_code == 200:
        return response.json()['data']['records']
    else:
        raise Exception(f"获取用户列表失败: {response.text}")

def delete_user(token, user_id):
    """删除用户"""
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR'
    }
    
    response = requests.delete(f'{base_url}/api/v1/user/{user_id}/', headers=headers)
    return response

def main():
    print("🧪 测试用户删除功能")
    print("=" * 40)
    
    try:
        # 1. 登录
        print("🔐 登录系统...")
        token = login('mpr_admin', 'admin123')
        print("✅ 登录成功!")
        
        # 2. 获取用户列表，找到测试用户
        print("\n📋 获取用户列表...")
        users = get_user_list(token)
        
        # 查找测试用户
        test_users = [user for user in users if 'test_' in user.get('userName', '')]
        
        if not test_users:
            print("❌ 没有找到测试用户")
            return
        
        print(f"✅ 找到 {len(test_users)} 个测试用户:")
        for user in test_users:
            print(f"  - {user['userName']} (ID: {user['id']}) - {user['roleDisplay']}")
        
        # 3. 删除第一个测试用户
        user_to_delete = test_users[0]
        print(f"\n🗑️ 删除用户: {user_to_delete['userName']} (ID: {user_to_delete['id']})")
        
        response = delete_user(token, user_to_delete['id'])
        print(f"删除响应状态码: {response.status_code}")
        print(f"删除响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 删除成功!")
            
            # 4. 验证删除结果
            print("\n🔍 验证删除结果...")
            updated_users = get_user_list(token)
            
            # 检查用户是否还存在
            deleted_user_exists = any(user['id'] == user_to_delete['id'] for user in updated_users)
            
            if not deleted_user_exists:
                print("✅ 用户已从列表中移除")
            else:
                print("❌ 用户仍然存在于列表中")
                
        else:
            print(f"❌ 删除失败: {response.text}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
