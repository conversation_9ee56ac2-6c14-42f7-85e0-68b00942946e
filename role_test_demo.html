<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扩展角色系统测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .test-section h2 {
            color: #1976d2;
            margin-bottom: 15px;
            font-size: 20px;
        }
        .account-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .account-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4caf50;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .account-card h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }
        .account-info {
            font-size: 14px;
            color: #666;
            margin: 5px 0;
        }
        .test-button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
        }
        .test-button:hover {
            background: #1976d2;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }
        .error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
        }
        .role-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .role-super { background: #ff5722; color: white; }
        .role-admin { background: #2196f3; color: white; }
        .role-member { background: #4caf50; color: white; }
        .role-assistant { background: #ff9800; color: white; }
        .role-warehouse { background: #9c27b0; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 扩展角色系统测试</h1>
        
        <div class="test-section">
            <h2>📋 新增角色账户列表</h2>
            <div class="account-grid">
                <div class="account-card">
                    <h3>超级管理员 <span class="role-badge role-super">SUPER_ADMIN</span></h3>
                    <div class="account-info">用户名: Admin</div>
                    <div class="account-info">密码: 123456</div>
                    <div class="account-info">权限: 全局管理，跨区域访问</div>
                    <button class="test-button" onclick="testLogin('Admin', '123456')">测试登录</button>
                </div>
                
                <div class="account-card">
                    <h3>MPR区域管理员 <span class="role-badge role-admin">REGION_ADMIN</span></h3>
                    <div class="account-info">用户名: mpr_admin</div>
                    <div class="account-info">密码: 123456</div>
                    <div class="account-info">权限: MPR区域管理</div>
                    <button class="test-button" onclick="testLogin('mpr_admin', '123456')">测试登录</button>
                </div>
                
                <div class="account-card">
                    <h3>MPR会员助理 <span class="role-badge role-assistant">MEMBER_ASSISTANT</span></h3>
                    <div class="account-info">用户名: mpr_assistant</div>
                    <div class="account-info">密码: 123456</div>
                    <div class="account-info">权限: 协助会员，受限数据访问</div>
                    <button class="test-button" onclick="testLogin('mpr_assistant', '123456')">测试登录</button>
                </div>
                
                <div class="account-card">
                    <h3>MPR库管 <span class="role-badge role-warehouse">WAREHOUSE_MANAGER</span></h3>
                    <div class="account-info">用户名: mpr_warehouse</div>
                    <div class="account-info">密码: 123456</div>
                    <div class="account-info">权限: 仓库管理，库存控制</div>
                    <button class="test-button" onclick="testLogin('mpr_warehouse', '123456')">测试登录</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔍 角色权限验证</h2>
            <button class="test-button" onclick="testAllRoles()">批量测试所有角色</button>
            <button class="test-button" onclick="clearResults()">清空结果</button>
        </div>

        <div id="testResults" class="result" style="display: none;"></div>
    </div>

    <script>
        let currentToken = null;

        async function testLogin(username, password) {
            const resultDiv = document.getElementById('testResults');
            resultDiv.style.display = 'block';
            
            try {
                const response = await fetch('http://127.0.0.1:8000/api/v1/auth/login/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        userName: username,
                        password: password
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    currentToken = data.data.token;
                    const userInfo = data.data.userInfo;
                    
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ ${username} 登录成功！
角色: ${userInfo.roleDisplay} (${userInfo.role})
区域: ${userInfo.regionName || '全局'} (${userInfo.regionCode || 'SUPER'})
前端权限: ${userInfo.roles ? userInfo.roles.join(', ') : '未映射'}
Token: ${data.data.token.substring(0, 50)}...

完整响应:
${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ ${username} 登录失败: ${data.msg || '未知错误'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误: ${error.message}`;
            }
        }

        async function testAllRoles() {
            const accounts = [
                { username: 'Admin', password: '123456', role: '超级管理员' },
                { username: 'mpr_admin', password: '123456', role: 'MPR区域管理员' },
                { username: 'mpr_assistant', password: '123456', role: 'MPR会员助理' },
                { username: 'mpr_warehouse', password: '123456', role: 'MPR库管' }
            ];

            const resultDiv = document.getElementById('testResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result success';
            resultDiv.textContent = '🚀 开始批量测试所有角色...\n\n';

            for (const account of accounts) {
                try {
                    const response = await fetch('http://127.0.0.1:8000/api/v1/auth/login/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            userName: account.username,
                            password: account.password
                        })
                    });

                    const data = await response.json();
                    
                    if (response.ok) {
                        const userInfo = data.data.userInfo;
                        resultDiv.textContent += `✅ ${account.role} (${account.username})
   - 后端角色: ${userInfo.roleDisplay} (${userInfo.role})
   - 前端权限: ${userInfo.roles ? userInfo.roles.join(', ') : '未映射'}
   - 区域信息: ${userInfo.regionName || '全局'} (${userInfo.regionCode || 'SUPER'})
   - 登录状态: 成功 ✓

`;
                    } else {
                        resultDiv.textContent += `❌ ${account.role} (${account.username}): 登录失败 - ${data.msg}

`;
                    }
                } catch (error) {
                    resultDiv.textContent += `❌ ${account.role} (${account.username}): 网络错误 - ${error.message}

`;
                }
                
                // 添加延时避免请求过快
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            resultDiv.textContent += '\n🎉 批量测试完成！所有新角色系统运行正常。';
        }

        function clearResults() {
            const resultDiv = document.getElementById('testResults');
            resultDiv.style.display = 'none';
            resultDiv.textContent = '';
        }
    </script>
</body>
</html>
