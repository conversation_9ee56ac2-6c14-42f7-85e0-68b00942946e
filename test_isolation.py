#!/usr/bin/env python3
import requests

def test_data_isolation():
    """测试数据隔离效果"""
    
    regions = [
        {'region': 'MPR', 'username': 'mpr_admin'},
        {'region': 'RL', 'username': 'rl_admin'},
        {'region': 'EO', 'username': 'eo_admin'},
    ]

    print('🔍 测试数据隔离效果...')
    print('=' * 50)

    for region_info in regions:
        region_code = region_info['region']
        username = region_info['username']
        
        # 登录获取token
        response = requests.post('http://localhost:8000/api/v1/auth/login/', 
            headers={'Content-Type': 'application/json', 'X-Region-Code': region_code},
            json={'userName': username, 'password': 'admin123'}
        )
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('data', {}).get('token', '')
            
            # 查看区域数据
            list_response = requests.get('http://localhost:8000/api/v1/business/test-data/list/',
                headers={'Authorization': token, 'X-Region-Code': region_code}
            )
            
            if list_response.status_code == 200:
                data = list_response.json()
                customers = data.get('customers', [])
                products = data.get('products', [])
                orders = data.get('orders', [])
                
                print(f'📋 {region_code} 区域数据统计:')
                print(f'   客户数量: {len(customers)}')
                print(f'   产品数量: {len(products)}')
                print(f'   订单数量: {len(orders)}')
                
                if customers:
                    customer = customers[0]
                    name = customer.get('name', 'N/A')
                    code = customer.get('customer_code', 'N/A')
                    print(f'   示例客户: {name} (编码: {code})')
                
                if products:
                    product = products[0]
                    name = product.get('name', 'N/A')
                    code = product.get('product_code', 'N/A')
                    print(f'   示例产品: {name} (编码: {code})')
                print()
            else:
                print(f'❌ 获取 {region_code} 区域数据失败: {list_response.status_code}')
                print(f'   错误信息: {list_response.text[:200]}...')
                print()

    print('✅ 数据隔离测试完成!')

if __name__ == '__main__':
    test_data_isolation()
