#!/usr/bin/env python3
import requests
import json

# 测试权限管理对话框调试
base_url = 'http://localhost:8000'

def login(username, password):
    """登录获取token"""
    login_data = {
        'userName': username,
        'password': password
    }
    
    response = requests.post(f'{base_url}/api/v1/auth/login/', 
        data=login_data,
        headers={'X-Region-Code': 'MPR'}
    )
    
    if response.status_code == 200:
        return response.json()['data']['token']
    else:
        raise Exception(f"登录失败: {response.text}")

def test_permission_dialog_apis(token):
    """测试权限对话框相关的所有API"""
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR'
    }
    
    print("🔍 测试权限对话框API调用...")
    
    # 1. 获取用户列表
    print("1️⃣ 测试用户列表API...")
    users_response = requests.get(f'{base_url}/api/v1/user/list/', headers=headers)
    if users_response.status_code == 200:
        users = users_response.json()['data']['records']
        print(f"✅ 用户列表API正常，共 {len(users)} 个用户")
        
        # 选择测试用户
        test_user = users[1] if len(users) > 1 else users[0]  # 选择第二个用户
        user_id = test_user['id']
        print(f"📋 选择测试用户: {test_user['userName']} (ID: {user_id})")
        
        # 2. 测试用户详情API (权限对话框会调用)
        print("2️⃣ 测试用户详情API...")
        detail_response = requests.get(f'{base_url}/api/v1/user/{user_id}/', headers=headers)
        if detail_response.status_code == 200:
            user_detail = detail_response.json()['data']
            print(f"✅ 用户详情API正常")
            print(f"   响应结构: {list(user_detail.keys())}")
            print(f"   用户名: {user_detail.get('username', 'N/A')}")
            print(f"   邮箱: {user_detail.get('email', 'N/A')}")
            print(f"   电话: {user_detail.get('phone', 'N/A')}")
            print(f"   角色: {user_detail.get('role', 'N/A')} ({user_detail.get('roleDisplay', 'N/A')})")
            print(f"   权限数量: {len(user_detail.get('permissions', []))}")
            print(f"   会员关联: {len(user_detail.get('responsibleMembers', []))}")
            
            # 检查必需字段
            required_fields = ['username', 'email', 'phone', 'role']
            missing_fields = [field for field in required_fields if not user_detail.get(field)]
            if missing_fields:
                print(f"⚠️ 缺少必需字段: {missing_fields}")
            else:
                print("✅ 所有必需字段都存在")
        else:
            print(f"❌ 用户详情API失败: {detail_response.text}")
            return False
        
        # 3. 测试权限列表API
        print("3️⃣ 测试权限列表API...")
        perm_response = requests.get(f'{base_url}/api/v1/user/permissions/', headers=headers)
        if perm_response.status_code == 200:
            perm_data = perm_response.json()['data']
            print(f"✅ 权限列表API正常")
            print(f"   响应结构: {list(perm_data.keys())}")
            print(f"   权限总数: {len(perm_data.get('permissions', []))}")
            print(f"   分类数量: {len(perm_data.get('categories', {}))}")
            print(f"   分类列表: {list(perm_data.get('categories', {}).keys())}")
        else:
            print(f"❌ 权限列表API失败: {perm_response.text}")
            return False
        
        # 4. 测试会员列表API (如果用户是助理)
        print("4️⃣ 测试会员列表API...")
        member_response = requests.get(f'{base_url}/api/v1/user/members/', headers=headers)
        if member_response.status_code == 200:
            member_data = member_response.json()['data']
            print(f"✅ 会员列表API正常")
            print(f"   会员数量: {len(member_data) if isinstance(member_data, list) else 'N/A'}")
        else:
            print(f"⚠️ 会员列表API失败: {member_response.text} (可能正常，取决于权限)")
        
        # 5. 模拟权限更新
        print("5️⃣ 测试权限更新API...")
        current_permissions = [p['id'] for p in user_detail.get('permissions', [])]
        update_data = {
            'username': user_detail['username'],
            'email': user_detail.get('email', ''),
            'phone': user_detail.get('phone', ''),
            'role': user_detail['role'],
            'permission_ids': current_permissions  # 保持当前权限不变
        }
        
        print(f"   更新数据: {update_data}")
        
        update_response = requests.put(f'{base_url}/api/v1/user/{user_id}/', 
            json=update_data, headers=headers)
        
        if update_response.status_code == 200:
            print(f"✅ 权限更新API正常")
            return True
        else:
            print(f"❌ 权限更新API失败: {update_response.text}")
            return False
    else:
        print(f"❌ 用户列表API失败: {users_response.text}")
        return False

def main():
    print("🐛 权限对话框调试测试")
    print("=" * 60)
    
    try:
        # 登录
        print("🔐 登录系统...")
        token = login('mpr_admin', 'admin123')
        print("✅ 登录成功!")
        
        # 测试API
        print("\n🔄 测试权限对话框相关API...")
        api_success = test_permission_dialog_apis(token)
        
        if api_success:
            print("\n🎉 所有API测试通过！")
            print("\n📋 前端调试步骤:")
            print("1. 打开浏览器开发者工具 (F12)")
            print("2. 切换到Console标签页")
            print("3. 打开 http://localhost:3010/#/system/user")
            print("4. 点击任意用户的'权限'按钮")
            print("5. 观察控制台输出的调试信息:")
            print("   - 权限管理对话框打开")
            print("   - 开始加载用户详情")
            print("   - 用户详情API响应")
            print("   - 开始加载权限列表")
            print("   - 权限列表API响应")
            print("   - 数据加载完成")
            print("6. 如果看到错误，请检查:")
            print("   - API响应格式是否正确")
            print("   - 数据字段是否完整")
            print("   - 网络请求是否成功")
            
            print("\n🔧 已添加的调试功能:")
            print("✅ 详细的控制台日志")
            print("✅ API响应数据验证")
            print("✅ 错误处理和提示")
            print("✅ 数据加载状态跟踪")
            print("✅ 字段完整性检查")
        else:
            print("\n❌ API测试失败，请检查后端服务！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
