from django.db import connections
from core.models import CustomUser, Region

print('🗄️ 当前数据库配置:')
print('=' * 50)

for alias in connections:
    db = connections[alias]
    db_name = db.settings_dict['NAME']
    print(f'  {alias}: {db_name}')

print('\n👥 主数据库用户:')
users = CustomUser.objects.all()
for user in users:
    region_name = user.region.name if user.region else '全局'
    print(f'  - {user.username} ({user.role}) - {region_name}')

print('\n🏢 区域配置:')
regions = Region.objects.all()
for region in regions:
    print(f'  - {region.name} ({region.code})')
