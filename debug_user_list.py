#!/usr/bin/env python3
import requests
import json

# 调试用户列表API
base_url = 'http://localhost:8000'

# 登录
login_data = {
    'userName': 'mpr_admin',
    'password': 'admin123'
}

response = requests.post(f'{base_url}/api/v1/auth/login/', 
    data=login_data,
    headers={'X-Region-Code': 'MPR'}
)

if response.status_code == 200:
    data = response.json()
    token = data['data']['token']
    
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR'
    }
    
    # 获取用户列表
    response = requests.get(f'{base_url}/api/v1/user/list/', headers=headers)
    print(f"状态码: {response.status_code}")
    print(f"完整响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
else:
    print(f"登录失败: {response.text}")
