# Generated by Django 5.2.3 on 2025-07-08 03:24

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='customer',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='customer',
            name='updated_by',
        ),
        migrations.RemoveField(
            model_name='inventory',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='inventory',
            name='updated_by',
        ),
        migrations.RemoveField(
            model_name='order',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='order',
            name='updated_by',
        ),
        migrations.RemoveField(
            model_name='orderitem',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='orderitem',
            name='updated_by',
        ),
        migrations.RemoveField(
            model_name='product',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='product',
            name='updated_by',
        ),
        migrations.AddField(
            model_name='customer',
            name='created_by_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='创建者ID'),
        ),
        migrations.AddField(
            model_name='customer',
            name='updated_by_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='更新者ID'),
        ),
        migrations.AddField(
            model_name='inventory',
            name='created_by_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='创建者ID'),
        ),
        migrations.AddField(
            model_name='inventory',
            name='updated_by_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='更新者ID'),
        ),
        migrations.AddField(
            model_name='order',
            name='created_by_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='创建者ID'),
        ),
        migrations.AddField(
            model_name='order',
            name='updated_by_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='更新者ID'),
        ),
        migrations.AddField(
            model_name='orderitem',
            name='created_by_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='创建者ID'),
        ),
        migrations.AddField(
            model_name='orderitem',
            name='updated_by_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='更新者ID'),
        ),
        migrations.AddField(
            model_name='product',
            name='created_by_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='创建者ID'),
        ),
        migrations.AddField(
            model_name='product',
            name='updated_by_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='更新者ID'),
        ),
    ]
