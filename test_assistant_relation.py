#!/usr/bin/env python3
"""
测试会员助理关联功能
"""
import requests
import json

def test_assistant_relation():
    """测试会员助理关联功能"""
    
    # 1. 先登录获取token
    print("🔐 正在登录...")
    login_response = requests.post('http://localhost:8000/api/v1/auth/login/', 
        json={
            'userName': 'mpr_admin',
            'password': 'admin123'
        },
        headers={'Content-Type': 'application/json', 'X-Region-Code': 'MPR'}
    )
    
    print(f'登录状态码: {login_response.status_code}')
    if login_response.status_code == 200:
        data = login_response.json()
        print('✅ 登录成功!')
        response_data = data.get('data', {})
        token = response_data.get('token', '')
        print(f'Token: {token[:50]}...')
        
        # 2. 获取助理列表
        print('\n👥 获取助理列表...')
        assistant_response = requests.get('http://localhost:8000/api/v1/user/assistants/',
            headers={
                'Authorization': token,
                'X-Region-Code': 'MPR'
            }
        )
        
        print(f'获取助理列表状态码: {assistant_response.status_code}')
        if assistant_response.status_code == 200:
            assistant_data = assistant_response.json()
            assistants = assistant_data.get('data', [])
            print(f'✅ 找到 {len(assistants)} 个助理')
            for assistant in assistants:
                print(f'  - {assistant["username"]} ({assistant["email"]}) - 负责 {assistant["managedMemberCount"]} 个会员')
            
            # 3. 创建一个会员助理（如果没有的话）
            if not assistants:
                print('\n👤 创建会员助理...')
                create_assistant_response = requests.post('http://localhost:8000/api/v1/user/list/',
                    json={
                        'username': 'test_assistant_001',
                        'email': '<EMAIL>',
                        'phone': '13800138002',
                        'role': 'MEMBER_ASSISTANT',
                        'password': 'test123456'
                    },
                    headers={
                        'Authorization': token,
                        'Content-Type': 'application/json',
                        'X-Region-Code': 'MPR'
                    }
                )
                
                print(f'创建助理状态码: {create_assistant_response.status_code}')
                if create_assistant_response.status_code == 201:
                    assistant_info = create_assistant_response.json().get('data', {})
                    print(f'✅ 助理创建成功: {assistant_info["username"]}')
                    assistant_id = assistant_info['id']
                else:
                    print('❌ 助理创建失败')
                    return
            else:
                assistant_id = assistants[0]['id']
                print(f'使用现有助理: {assistants[0]["username"]} (ID: {assistant_id})')
            
            # 4. 创建一个会员并关联助理
            print(f'\n👤 创建会员并关联助理 (ID: {assistant_id})...')
            create_member_response = requests.post('http://localhost:8000/api/v1/user/list/',
                json={
                    'username': 'test_member_with_assistant',
                    'email': '<EMAIL>',
                    'phone': '13800138003',
                    'role': 'MEMBER',
                    'password': 'test123456',
                    'managed_by_id': assistant_id
                },
                headers={
                    'Authorization': token,
                    'Content-Type': 'application/json',
                    'X-Region-Code': 'MPR'
                }
            )
            
            print(f'创建会员状态码: {create_member_response.status_code}')
            if create_member_response.status_code == 201:
                member_info = create_member_response.json().get('data', {})
                print(f'✅ 会员创建成功: {member_info["username"]}')
                print(f'关联助理: {member_info.get("managedByName", "未关联")}')
            else:
                print('❌ 会员创建失败')
                print(f'响应: {create_member_response.text}')
                return
            
            # 5. 验证用户列表中的助理信息
            print('\n📋 验证用户列表中的助理信息...')
            list_response = requests.get('http://localhost:8000/api/v1/user/list/?current=1&size=20',
                headers={
                    'Authorization': token,
                    'X-Region-Code': 'MPR'
                }
            )
            
            if list_response.status_code == 200:
                list_data = list_response.json()
                users = list_data.get('data', {}).get('records', [])
                
                # 查找刚创建的会员
                test_member = None
                for user in users:
                    if user.get('userName') == 'test_member_with_assistant':
                        test_member = user
                        break
                
                if test_member:
                    print('✅ 找到测试会员!')
                    print(f'会员信息: {test_member["userName"]} ({test_member["userEmail"]})')
                    print(f'负责助理: {test_member.get("managedByName", "未分配")}')
                    print(f'助理邮箱: {test_member.get("managedByEmail", "无")}')
                else:
                    print('❌ 未找到测试会员')
            else:
                print('❌ 获取用户列表失败')
                
        else:
            print('❌ 获取助理列表失败')
            print(f'响应: {assistant_response.text}')
            
    else:
        print('❌ 登录失败')
        print(f'响应: {login_response.text}')

if __name__ == '__main__':
    test_assistant_relation()
