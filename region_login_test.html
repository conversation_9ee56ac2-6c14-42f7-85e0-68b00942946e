<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>区域登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .login-form {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .user-accounts {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .account-item {
            margin: 5px 0;
            padding: 8px;
            background: #e9ecef;
            border-radius: 3px;
            cursor: pointer;
        }
        .account-item:hover {
            background: #dee2e6;
        }
        input, button {
            padding: 10px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐾 区域登录系统测试</h1>
        
        <div class="user-accounts">
            <h3>可用测试账户：</h3>
            <div class="account-item" onclick="fillLogin('Admin', '123456')">
                <strong>Admin</strong> - 超级管理员 (全局管理)
            </div>
            <div class="account-item" onclick="fillLogin('mpr_admin', '123456')">
                <strong>mpr_admin</strong> - MPR区域管理员
            </div>
            <div class="account-item" onclick="fillLogin('eo_admin', '123456')">
                <strong>eo_admin</strong> - EO区域管理员
            </div>
            <div class="account-item" onclick="fillLogin('rl_admin', '123456')">
                <strong>rl_admin</strong> - RL区域管理员
            </div>
            <div class="account-item" onclick="fillLogin('zz_admin', '123456')">
                <strong>zz_admin</strong> - ZZ区域管理员
            </div>
            <div class="account-item" onclick="fillLogin('wh_admin', '123456')">
                <strong>wh_admin</strong> - WH区域管理员
            </div>
        </div>

        <div class="login-form">
            <h3>登录测试</h3>
            <input type="text" id="username" placeholder="用户名" />
            <input type="password" id="password" placeholder="密码" />
            <button onclick="testLogin()">登录</button>
            <button onclick="getUserInfo()" id="getUserInfoBtn" disabled>获取用户信息</button>
        </div>

        <div id="result"></div>
    </div>

    <script>
        let currentToken = null;

        function fillLogin(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showResult('请输入用户名和密码', 'error');
                return;
            }

            try {
                const response = await fetch('http://127.0.0.1:8000/api/v1/auth/login/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        userName: username,
                        password: password
                    })
                });

                const data = await response.json();
                
                if (data.code === 200) {
                    currentToken = data.data.token;
                    document.getElementById('getUserInfoBtn').disabled = false;
                    showResult(`登录成功！\n\n用户信息：\n${JSON.stringify(data.data.userInfo, null, 2)}\n\nToken: ${data.data.token.substring(0, 50)}...`, 'success');
                } else {
                    showResult(`登录失败：${data.msg}`, 'error');
                }
            } catch (error) {
                showResult(`请求失败：${error.message}`, 'error');
            }
        }

        async function getUserInfo() {
            if (!currentToken) {
                showResult('请先登录', 'error');
                return;
            }

            try {
                const response = await fetch('http://127.0.0.1:8000/api/v1/user/info/', {
                    method: 'GET',
                    headers: {
                        'Authorization': currentToken,
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();
                
                if (data.code === 200) {
                    showResult(`获取用户信息成功！\n\n${JSON.stringify(data.data, null, 2)}`, 'success');
                } else {
                    showResult(`获取用户信息失败：${data.msg}`, 'error');
                }
            } catch (error) {
                showResult(`请求失败：${error.message}`, 'error');
            }
        }

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
        }
    </script>
</body>
</html>
