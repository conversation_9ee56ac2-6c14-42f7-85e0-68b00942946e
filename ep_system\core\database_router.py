# core/database_router.py
"""
多数据库路由系统
实现按区域分库的数据库路由逻辑
"""

from .middleware import get_current_region


class RegionDatabaseRouter:
    """
    区域数据库路由器
    根据当前区域和模型类型，将数据库操作路由到对应的数据库
    """
    
    # 主数据库中的模型（全局共享数据）
    MASTER_DB_MODELS = {
        'core.CustomUser',      # 用户认证信息
        'core.Region',          # 区域配置
        'core.Permission',      # 自定义权限
        'core.UserPermission',  # 用户权限关联
        'auth.User',            # Django默认用户模型
        'auth.Group',           # 用户组
        'auth.Permission',      # Django权限
        'contenttypes.ContentType',  # 内容类型
        'sessions.Session',     # 会话
        'admin.LogEntry',       # 管理日志
    }
    
    # 区域数据库映射
    REGION_DB_MAPPING = {
        'MPR': 'mpr_db',
        'RL': 'rl_db', 
        'EO': 'eo_db',
        'ZZ': 'zz_db',
        'WH': 'wh_db',
    }
    
    def db_for_read(self, model, **hints):
        """
        决定读取操作使用哪个数据库
        """
        model_name = f"{model._meta.app_label}.{model._meta.object_name}"
        
        # 主数据库模型始终使用默认数据库
        if model_name in self.MASTER_DB_MODELS:
            return 'default'
        
        # 获取当前区域
        current_region = get_current_region()
        if current_region and current_region.code in self.REGION_DB_MAPPING:
            return self.REGION_DB_MAPPING[current_region.code]
        
        # 如果没有区域信息，使用默认数据库
        return 'default'
    
    def db_for_write(self, model, **hints):
        """
        决定写入操作使用哪个数据库
        """
        model_name = f"{model._meta.app_label}.{model._meta.object_name}"
        
        # 主数据库模型始终使用默认数据库
        if model_name in self.MASTER_DB_MODELS:
            return 'default'
        
        # 获取当前区域
        current_region = get_current_region()
        if current_region and current_region.code in self.REGION_DB_MAPPING:
            return self.REGION_DB_MAPPING[current_region.code]
        
        # 如果没有区域信息，使用默认数据库
        return 'default'
    
    def allow_relation(self, obj1, obj2, **hints):
        """
        决定是否允许两个对象之间的关系
        """
        # 获取两个对象的数据库
        db_set = {obj1._state.db, obj2._state.db}
        
        # 如果两个对象在同一个数据库中，允许关系
        if len(db_set) == 1:
            return True
        
        # 如果其中一个对象在主数据库中，允许关系（用于外键关联）
        if 'default' in db_set:
            return True
        
        # 否则不允许跨区域数据库的关系
        return False
    
    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """
        决定是否允许在指定数据库中进行迁移
        """
        model_full_name = f"{app_label}.{model_name}" if model_name else None
        
        # 默认数据库：只允许主数据库模型的迁移
        if db == 'default':
            if model_full_name:
                return model_full_name in self.MASTER_DB_MODELS
            # 如果没有指定模型，允许核心应用的迁移
            return app_label in ['core', 'auth', 'contenttypes', 'sessions', 'admin']
        
        # 区域数据库：不允许主数据库模型的迁移
        if db in self.REGION_DB_MAPPING.values():
            if model_full_name:
                return model_full_name not in self.MASTER_DB_MODELS
            # 允许业务应用的迁移
            return app_label not in ['auth', 'contenttypes', 'sessions', 'admin']
        
        # 其他情况不允许迁移
        return False


class DatabaseManager:
    """
    数据库管理器
    提供便捷的数据库操作方法
    """
    
    @staticmethod
    def get_region_db_name(region_code):
        """
        根据区域代码获取数据库名称
        """
        return RegionDatabaseRouter.REGION_DB_MAPPING.get(region_code)
    
    @staticmethod
    def get_current_db():
        """
        获取当前应该使用的数据库
        """
        current_region = get_current_region()
        if current_region and current_region.code in RegionDatabaseRouter.REGION_DB_MAPPING:
            return RegionDatabaseRouter.REGION_DB_MAPPING[current_region.code]
        return 'default'
    
    @staticmethod
    def get_all_region_dbs():
        """
        获取所有区域数据库列表
        """
        return list(RegionDatabaseRouter.REGION_DB_MAPPING.values())
    
    @staticmethod
    def is_master_db_model(model):
        """
        判断模型是否属于主数据库
        """
        model_name = f"{model._meta.app_label}.{model._meta.object_name}"
        return model_name in RegionDatabaseRouter.MASTER_DB_MODELS


# 便捷的数据库操作装饰器
def use_region_db(region_code):
    """
    装饰器：强制使用指定区域的数据库
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            db_name = DatabaseManager.get_region_db_name(region_code)
            if db_name:
                # 这里可以添加临时切换数据库的逻辑
                pass
            return func(*args, **kwargs)
        return wrapper
    return decorator


def use_master_db(func):
    """
    装饰器：强制使用主数据库
    """
    def wrapper(*args, **kwargs):
        # 这里可以添加强制使用主数据库的逻辑
        return func(*args, **kwargs)
    return wrapper
