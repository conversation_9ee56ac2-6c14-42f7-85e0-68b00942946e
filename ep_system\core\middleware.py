# core/middleware.py
import threading
from django.core.cache import cache
from .models import Region

# 线程局部存储，用于存储当前请求的区域信息
_thread_locals = threading.local()

def get_current_region():
    """获取当前请求的区域对象"""
    return getattr(_thread_locals, 'region', None)

class RegionMiddleware:
    """区域中间件，用于处理多区域请求"""
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # 在处理请求前设置区域信息
        self.process_request(request)
        
        response = self.get_response(request)
        
        # 清理线程局部变量
        self.process_response(request, response)
        
        return response

    def process_request(self, request):
        """处理请求，设置当前区域"""
        _thread_locals.region = None
        region_code = request.headers.get('X-Region-Code')
        if region_code:
            # 1. 优先从缓存获取
            cache_key = f"region_{region_code}"
            region = cache.get(cache_key)
            if not region:
                # 2. 缓存未命中，再查数据库
                try:
                    region = Region.objects.get(code__iexact=region_code)
                    # 3. 将结果存入缓存，设置一个较长的过期时间
                    cache.set(cache_key, region, timeout=3600)  # 缓存1小时
                except Region.DoesNotExist:
                    pass
            _thread_locals.region = region

    def process_response(self, request, response):
        """处理响应，清理线程局部变量"""
        if hasattr(_thread_locals, 'region'):
            delattr(_thread_locals, 'region')
        return response
