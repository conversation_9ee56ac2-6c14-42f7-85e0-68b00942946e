# core/middleware.py
import threading
from django.core.cache import cache
from .models import Region
from .domain_config import get_region_code_from_domain, get_domain_info

# 线程局部存储，用于存储当前请求的区域信息和用户信息
_thread_locals = threading.local()

def get_current_region():
    """获取当前请求的区域对象"""
    return getattr(_thread_locals, 'region', None)

def get_current_user():
    """获取当前请求的用户对象"""
    return getattr(_thread_locals, 'user', None)

def get_current_domain_info():
    """获取当前请求的域名信息"""
    return getattr(_thread_locals, 'domain_info', None)

class RegionMiddleware:
    """区域中间件，用于处理多区域请求"""
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # 在处理请求前设置区域信息
        self.process_request(request)
        
        response = self.get_response(request)
        
        # 清理线程局部变量
        self.process_response(request, response)
        
        return response

    def process_request(self, request):
        """处理请求，设置当前区域和用户"""
        _thread_locals.region = None
        _thread_locals.domain_info = None
        _thread_locals.user = None

        # 存储当前用户信息（如果已认证）
        if hasattr(request, 'user') and request.user.is_authenticated:
            _thread_locals.user = request.user

        # 获取域名信息
        host = request.get_host()
        domain_info = get_domain_info(host)
        _thread_locals.domain_info = domain_info

        # 方法1: 优先从请求头获取区域代码（用于API调用）
        region_code = request.headers.get('X-Region-Code')

        # 方法2: 从域名自动识别区域（用于多域名访问）
        if not region_code:
            region_code = domain_info.get('region_code')

        if region_code:
            # 1. 优先从缓存获取
            cache_key = f"region_{region_code}"
            region = cache.get(cache_key)
            if not region:
                # 2. 缓存未命中，再查数据库
                try:
                    region = Region.objects.get(code__iexact=region_code)
                    # 3. 将结果存入缓存，设置一个较长的过期时间
                    cache.set(cache_key, region, timeout=3600)  # 缓存1小时
                except Region.DoesNotExist:
                    pass
            _thread_locals.region = region

    def extract_region_from_domain(self, request):
        """从域名中提取区域代码"""
        host = request.get_host()
        return get_region_code_from_domain(host)

    def process_response(self, request, response):
        """处理响应，清理线程局部变量"""
        if hasattr(_thread_locals, 'region'):
            delattr(_thread_locals, 'region')
        if hasattr(_thread_locals, 'domain_info'):
            delattr(_thread_locals, 'domain_info')
        if hasattr(_thread_locals, 'user'):
            delattr(_thread_locals, 'user')
        return response
