#!/usr/bin/env python3
import requests
import json

# 检查新创建的用户
base_url = 'http://localhost:8000'

# 登录
login_data = {
    'userName': 'mpr_admin',
    'password': 'admin123'
}

response = requests.post(f'{base_url}/api/v1/auth/login/', 
    data=login_data,
    headers={'X-Region-Code': 'MPR'}
)

if response.status_code == 200:
    data = response.json()
    token = data['data']['token']
    
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR'
    }
    
    # 获取用户列表，增加分页参数
    response = requests.get(f'{base_url}/api/v1/user/list/?current=1&size=20', headers=headers)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()['data']
        users = data.get('records', [])
        print(f"总共 {data.get('total', 0)} 个用户，当前页显示 {len(users)} 个")
        
        # 查找最新创建的用户
        for user in users:
            if 'test_frontend_member_' in user.get('userName', ''):
                print(f"\n✅ 找到测试用户:")
                print(f"  用户名: {user['userName']}")
                print(f"  邮箱: {user['userEmail']}")
                print(f"  角色: {user['roleDisplay']}")
                if user.get('managedById'):
                    print(f"  负责助理: {user['managedByName']} ({user['managedByEmail']})")
                else:
                    print(f"  负责助理: 未关联")
                break
        else:
            print("❌ 未找到测试用户")
            print("所有用户列表:")
            for user in users:
                print(f"  - {user['userName']} ({user['userEmail']}) - {user['roleDisplay']}")
    else:
        print(f"获取用户列表失败: {response.text}")
else:
    print(f"登录失败: {response.text}")
