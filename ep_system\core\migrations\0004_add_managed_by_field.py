# Generated by Django 5.2.3 on 2025-07-08 07:08

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0003_add_new_roles'),
    ]

    operations = [
        migrations.AddField(
            model_name='customuser',
            name='managed_by',
            field=models.ForeignKey(blank=True, help_text='仅对会员(MEMBER)角色有效，指向负责该会员的助理', limit_choices_to={'role': 'MEMBER_ASSISTANT'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_members', to=settings.AUTH_USER_MODEL, verbose_name='负责助理'),
        ),
    ]
