#!/usr/bin/env python3
"""
助理关联系统综合测试
测试完整的助理-会员关联功能流程
"""
import requests
import json
import time

base_url = 'http://localhost:8000'

def login(username, password):
    """登录并返回token"""
    login_data = {
        'userName': username,
        'password': password
    }
    
    response = requests.post(f'{base_url}/api/v1/auth/login/', 
        data=login_data,
        headers={'X-Region-Code': 'MPR'}
    )
    
    if response.status_code == 200:
        return response.json()['data']['token']
    else:
        raise Exception(f"登录失败: {response.text}")

def get_assistants(token):
    """获取助理列表"""
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR'
    }
    
    response = requests.get(f'{base_url}/api/v1/user/assistants/', headers=headers)
    if response.status_code == 200:
        return response.json()['data']
    else:
        raise Exception(f"获取助理列表失败: {response.text}")

def create_member_with_assistant(token, username, email, assistant_id):
    """创建会员并关联助理"""
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR'
    }
    
    create_data = {
        'username': username,
        'email': email,
        'password': 'password123',
        'role': 'MEMBER',
        'managed_by_id': assistant_id
    }
    
    response = requests.post(f'{base_url}/api/v1/user/list/', 
        json=create_data, 
        headers=headers
    )
    
    if response.status_code == 201:
        return response.json()['data']
    else:
        raise Exception(f"创建会员失败: {response.text}")

def get_user_list(token):
    """获取用户列表"""
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR'
    }
    
    response = requests.get(f'{base_url}/api/v1/user/list/?current=1&size=50', headers=headers)
    if response.status_code == 200:
        return response.json()['data']['records']
    else:
        raise Exception(f"获取用户列表失败: {response.text}")

def main():
    print("🚀 开始助理关联系统综合测试")
    print("=" * 50)
    
    try:
        # 1. 登录
        print("🔐 步骤1: 登录系统...")
        token = login('mpr_admin', 'admin123')
        print("✅ 登录成功!")
        
        # 2. 获取助理列表
        print("\n👥 步骤2: 获取助理列表...")
        assistants = get_assistants(token)
        print(f"✅ 找到 {len(assistants)} 个助理:")
        for assistant in assistants:
            print(f"  - {assistant['username']} ({assistant['email']}) - 负责 {assistant['managedMemberCount']} 个会员")
        
        if not assistants:
            print("❌ 没有可用的助理，测试终止")
            return
        
        # 3. 创建新会员并关联助理
        print(f"\n👤 步骤3: 创建会员并关联助理...")
        timestamp = int(time.time())
        test_username = f'final_test_member_{timestamp}'
        test_email = f'final_test_{timestamp}@example.com'
        assistant_id = assistants[0]['id']
        assistant_name = assistants[0]['username']
        
        print(f"  创建会员: {test_username}")
        print(f"  关联助理: {assistant_name} (ID: {assistant_id})")
        
        new_member = create_member_with_assistant(token, test_username, test_email, assistant_id)
        print("✅ 会员创建成功!")
        print(f"  会员ID: {new_member['id']}")
        print(f"  用户名: {new_member['username']}")
        print(f"  邮箱: {new_member['email']}")
        
        # 4. 验证用户列表中的助理关联信息
        print(f"\n📋 步骤4: 验证用户列表中的助理关联...")
        users = get_user_list(token)
        
        # 查找刚创建的用户
        test_user = None
        for user in users:
            if user['userName'] == test_username:
                test_user = user
                break
        
        if test_user:
            print("✅ 在用户列表中找到测试会员!")
            print(f"  用户名: {test_user['userName']}")
            print(f"  邮箱: {test_user['userEmail']}")
            print(f"  角色: {test_user['roleDisplay']}")
            
            if test_user.get('managedById'):
                print(f"  ✅ 助理关联成功!")
                print(f"    负责助理ID: {test_user['managedById']}")
                print(f"    负责助理姓名: {test_user['managedByName']}")
                print(f"    负责助理邮箱: {test_user['managedByEmail']}")
                
                # 验证助理ID是否匹配
                if test_user['managedById'] == assistant_id:
                    print("  ✅ 助理ID匹配正确!")
                else:
                    print(f"  ❌ 助理ID不匹配! 期望: {assistant_id}, 实际: {test_user['managedById']}")
            else:
                print("  ❌ 助理关联失败，未找到助理信息")
        else:
            print("❌ 在用户列表中未找到测试会员")
        
        # 5. 重新获取助理列表，验证会员数量更新
        print(f"\n🔄 步骤5: 验证助理负责会员数量更新...")
        updated_assistants = get_assistants(token)
        
        for assistant in updated_assistants:
            if assistant['id'] == assistant_id:
                print(f"✅ 助理 {assistant['username']} 现在负责 {assistant['managedMemberCount']} 个会员")
                break
        
        print(f"\n🎉 综合测试完成!")
        print("=" * 50)
        print("✅ 所有功能测试通过:")
        print("  - 用户登录 ✅")
        print("  - 获取助理列表 ✅") 
        print("  - 创建会员并关联助理 ✅")
        print("  - 用户列表显示助理信息 ✅")
        print("  - 助理负责会员数量统计 ✅")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
