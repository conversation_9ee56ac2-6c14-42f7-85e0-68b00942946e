from django.db import models

# Create your models here.
# core/models.py
from django.db import models
from django.contrib.auth.models import AbstractUser
from .managers import RegionAwareManager

class Region(models.Model):
    """区域模型"""
    name = models.CharField(max_length=100, unique=True, verbose_name="区域名称")
    code = models.CharField(max_length=50, unique=True, verbose_name="区域代码")
    def __str__(self):
        return self.name


class Permission(models.Model):
    """权限模型"""
    name = models.CharField(max_length=100, unique=True, verbose_name="权限名称")
    code = models.CharField(max_length=50, unique=True, verbose_name="权限代码")
    description = models.TextField(blank=True, verbose_name="权限描述")
    category = models.CharField(max_length=50, verbose_name="权限分类")

    class Meta:
        verbose_name = "权限"
        verbose_name_plural = "权限"

    def __str__(self):
        return f"{self.name} ({self.code})"


class UserPermission(models.Model):
    """用户权限关联模型"""
    user = models.ForeignKey('CustomUser', on_delete=models.CASCADE, related_name='permission_grants')
    permission = models.ForeignKey(Permission, on_delete=models.CASCADE)
    granted_at = models.DateTimeField(auto_now_add=True, verbose_name="授权时间")
    granted_by = models.ForeignKey('CustomUser', on_delete=models.SET_NULL, null=True, related_name='granted_permissions', verbose_name="授权人")

    class Meta:
        unique_together = ('user', 'permission')
        verbose_name = "用户权限"
        verbose_name_plural = "用户权限"

    def __str__(self):
        return f"{self.user.username} - {self.permission.name}"

class CustomUser(AbstractUser):
    """自定义用户模型"""
    
    class Role(models.TextChoices):
        SUPER_ADMIN = 'SUPER_ADMIN', '超级管理员'
        REGION_ADMIN = 'REGION_ADMIN', '区域管理员'
        MEMBER = 'MEMBER', '普通会员'
        MEMBER_ASSISTANT = 'MEMBER_ASSISTANT', '会员助理'
        WAREHOUSE_MANAGER = 'WAREHOUSE_MANAGER', '库管'

    # 每个用户都必须属于一个区域
    region = models.ForeignKey(Region, on_delete=models.PROTECT, null=True, blank=True, verbose_name="所属区域")
    # 可以添加手机号等其他字段
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name="手机号")
    role = models.CharField(max_length=20, choices=Role.choices, default=Role.MEMBER, verbose_name="角色")
    email = models.EmailField(verbose_name="邮箱")

    # 会员助理关联：会员(MEMBER)通过此字段指向负责他们的助理(MEMBER_ASSISTANT)
    managed_by = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        limit_choices_to={'role': Role.MEMBER_ASSISTANT},
        related_name='managed_members',
        verbose_name="负责助理",
        help_text="仅对会员(MEMBER)角色有效，指向负责该会员的助理"
    )


    # 指定默认的模型管理器
    objects = RegionAwareManager()  # 覆盖默认的
    all_objects = models.Manager()

    class Meta:
        # 添加复合唯一约束，确保 email 在每个 region 内是唯一的
        constraints = [
            models.UniqueConstraint(fields=['region', 'email'], name='unique_email_in_region')
        ]

    def __str__(self):
        return self.username

    def has_permission(self, permission_code):
        """检查用户是否有指定权限"""
        # 超级管理员拥有所有权限
        if self.role == self.Role.SUPER_ADMIN:
            return True

        # 检查用户是否被直接授予该权限
        return self.permission_grants.filter(permission__code=permission_code).exists()

    def get_permissions(self):
        """获取用户所有权限"""
        if self.role == self.Role.SUPER_ADMIN:
            # 超级管理员拥有所有权限
            return Permission.objects.all()

        # 返回用户被授予的权限
        return Permission.objects.filter(userpermission__user=self)

    def get_permission_codes(self):
        """获取用户权限代码列表"""
        if self.role == self.Role.SUPER_ADMIN:
            return list(Permission.objects.values_list('code', flat=True))

        return list(self.permission_grants.values_list('permission__code', flat=True))

class Order(models.Model):
    """订单模型示例"""
    order_id = models.CharField(max_length=100, unique=True, verbose_name="订单号")
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="金额")
    # 订单创建者
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, verbose_name="关联用户")
    # 订单所属区域，通过 user 自动关联
    region = models.ForeignKey(Region, on_delete=models.PROTECT, verbose_name="所属区域")
    created_at = models.DateTimeField(auto_now_add=True)

    # 同样应用管理器
    objects = RegionAwareManager()
    all_objects = models.Manager()

    def save(self, *args, **kwargs):
        # 在保存订单时，自动将其区域设置为其关联用户的区域
        if not self.pk and self.user and self.user.region:
            self.region = self.user.region
        super().save(*args, **kwargs)

    def __str__(self):
        return self.order_id
