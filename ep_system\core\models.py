from django.db import models

# Create your models here.
# core/models.py
from django.db import models
from django.contrib.auth.models import AbstractUser
from .managers import RegionAwareManager

class Region(models.Model):
    """区域模型"""
    name = models.CharField(max_length=100, unique=True, verbose_name="区域名称")
    code = models.CharField(max_length=50, unique=True, verbose_name="区域代码")
    def __str__(self):
        return self.name

class CustomUser(AbstractUser):
    """自定义用户模型"""
    
    class Role(models.TextChoices):
        SUPER_ADMIN = 'SUPER_ADMIN', '超级管理员'
        REGION_ADMIN = 'REGION_ADMIN', '区域管理员'
        MEMBER = 'MEMBER', '普通会员'

    # 每个用户都必须属于一个区域
    region = models.ForeignKey(Region, on_delete=models.PROTECT, null=True, blank=True, verbose_name="所属区域")
    # 可以添加手机号等其他字段
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name="手机号")
    role = models.CharField(max_length=20, choices=Role.choices, default=Role.MEMBER, verbose_name="角色")
    email = models.EmailField(verbose_name="邮箱")


    # 指定默认的模型管理器
    objects = RegionAwareManager()  # 覆盖默认的
    all_objects = models.Manager()

    class Meta:
        # 添加复合唯一约束，确保 email 在每个 region 内是唯一的
        constraints = [
            models.UniqueConstraint(fields=['region', 'email'], name='unique_email_in_region')
        ]

    def __str__(self):
        return self.username

class Order(models.Model):
    """订单模型示例"""
    order_id = models.CharField(max_length=100, unique=True, verbose_name="订单号")
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="金额")
    # 订单创建者
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, verbose_name="关联用户")
    # 订单所属区域，通过 user 自动关联
    region = models.ForeignKey(Region, on_delete=models.PROTECT, verbose_name="所属区域")
    created_at = models.DateTimeField(auto_now_add=True)

    # 同样应用管理器
    objects = RegionAwareManager()
    all_objects = models.Manager()

    def save(self, *args, **kwargs):
        # 在保存订单时，自动将其区域设置为其关联用户的区域
        if not self.pk and self.user and self.user.region:
            self.region = self.user.region
        super().save(*args, **kwargs)

    def __str__(self):
        return self.order_id
