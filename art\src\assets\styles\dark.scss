/*
* 深色主题
* 单页面移除深色主题 document.getElementsByTagName("html")[0].removeAttribute('class')
*/

$font-color: rgba(#ffffff, 0.7);
$background-color: #070707;

/* 覆盖element-plus默认深色背景色 */
html.dark {
  // ✅ element-plus
  // --el-bg-color: $background-color;
  --el-text-color-regular: $font-color;

  // ✅ 富文本编辑器
  // 工具栏背景颜色
  --w-e-toolbar-bg-color: var(--art-main-bg-color);
  // 输入区域背景颜色
  --w-e-textarea-bg-color: var(--art-main-bg-color);
  // 工具栏文字颜色
  --w-e-toolbar-color: var(--art-text-gray-600);
  // 选中菜单颜色
  --w-e-toolbar-active-bg-color: rgba(var(--art-gray-100-rgb), 0.8);
  // 弹窗边框颜色
  --w-e-toolbar-border-color: var(--art-border-dashed-color);
  // 分割线颜色
  --w-e-textarea-border-color: var(--art-border-dashed-color);
  // 链接输入框边框颜色
  --w-e-modal-button-border-color: var(--art-border-dashed-color);
  // 表格头颜色
  --w-e-textarea-slight-bg-color: var(--art-color);
  // 按钮背景颜色
  --w-e-modal-button-bg-color: var(--art-color);
}

.dark {
  color: $font-color !important;
  background: $background-color !important;

  /* 全局文字颜色 */
  body {
    color: $font-color;

    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    .lang .btn,
    .layout-top-bar .user .name,
    .dark-text {
      color: $font-color !important;
    }
  }

  // 图片降低亮度
  img {
    filter: brightness(0.92) saturate(1.25);
  }

  .editor-wrapper {
    *:not(pre code *) {
      color: inherit !important;
    }
  }

  .img-cutter {
    *:not([class^='el-']) {
      color: inherit !important;
    }
  }

  // ✅ 左侧菜单样式
  .layout-sidebar,
  .dual-menu {
    .el-menu-dark {
      // 选中颜色
      .el-menu-item.is-active {
        background: transparent;
      }

      .el-sub-menu__title {
        .el-icon {
          color: var(--art-gray-800) !important;
        }
      }

      // 鼠标移入背景色
      .el-sub-menu__title:hover,
      .el-menu-item:not(.is-active):hover {
        background: rgba(var(--art-gray-200-rgb), 0.6) !important;
      }

      [level-item='2'].is-active:not(.el-menu--collapse) {
        &.is-active {
          &:before {
            margin-left: -10px !important;
          }
        }
      }

      .el-menu:not(.el-menu--collapse) {
        // 选中颜色
        .el-menu-item.is-active {
          &:before {
            content: '';
            width: 5px;
            height: 5px;
            border-radius: 50%;
            position: absolute;
            top: 0;
            bottom: 0;
            margin: auto;
            background: var(--main-color) !important;
            transition: all 0.2s;
            margin-left: -18px;
          }
        }
      }
    }
  }

  .page-content .article-list .item .left .outer > div {
    border-right-color: var(--dark-border-color) !important;
  }

  // ✅ 富文本编辑器
  // 分隔线
  .w-e-bar-divider {
    background-color: var(--art-gray-300) !important;
  }

  // 下拉选择框
  .w-e-select-list {
    background-color: var(--art-main-bg-color) !important;
    border: 1px solid var(--art-border-dashed-color) !important;
  }

  /* 弹出框 */
  .w-e-drop-panel {
    border: 1px solid var(--art-border-dashed-color) !important;
  }

  /* 工具栏菜单 */
  .w-e-bar-item-group .w-e-bar-item-menus-container {
    background-color: var(--art-main-bg-color) !important;
    border: 1px solid var(--art-border-dashed-color) !important;
  }

  /* 下拉选择框 hover 样式调整 */
  .w-e-select-list ul li:hover,
  /* 工具栏 hover 按钮背景颜色 */
  .w-e-bar-item button:hover {
    background-color: var(--art-color) !important;
  }

  /* 代码块 */
  .w-e-text-container [data-slate-editor] pre > code {
    background-color: var(--art-gray-100) !important;
    border: 1px solid var(--art-border-dashed-color) !important;
    text-shadow: none !important;
  }

  /* 引用 */
  .w-e-text-container [data-slate-editor] blockquote {
    border-left: 4px solid var(--art-gray-200) !important;
    background-color: var(--art-color);
  }

  .editor-wrapper {
    .w-e-text-container [data-slate-editor] .table-container th:last-of-type {
      border-right: 1px solid var(--art-gray-200) !important;
    }

    .w-e-modal {
      background-color: var(--art-color);
    }
  }

  // 工作台标签文字颜色
  .worktab .scroll-view .tabs li {
    color: var(--art-text-gray-800) !important;
  }

  // 顶部按钮文字颜色
  .layout-top-bar .btn-box .btn i,
  .fast-enter-trigger .btn i {
    color: var(--art-text-gray-700) !important;
  }
}

// 移动端文字颜色
@media screen and (max-width: $device-phone) {
  .dark {
    $font-color: rgba(#ffffff, 0.8);
    --el-text-color-regular: $font-color !important;
    color: $font-color !important;

    body {
      color: $font-color !important;

      h1,
      h2,
      h3,
      h4,
      h5,
      h6,
      .lang .btn,
      .layout-top-bar .user .name {
        color: $font-color !important;
      }
    }
  }
}
