# core/urls.py
from django.urls import path
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
)
from .views import (
    RegistrationAPIView,
    LoginAPIView,
    UserInfoAPIView,
    UserListAPIView,
    AssistantListAPIView,
    DomainInfoAPIView
)

app_name = 'core'

urlpatterns = [
    # 认证相关API
    path('auth/register/', RegistrationAPIView.as_view(), name='register'),
    path('auth/login/', LoginAPIView.as_view(), name='login'),  # 前端需要的登录接口
    path('auth/token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('auth/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),

    # 用户相关API
    path('user/info/', UserInfoAPIView.as_view(), name='user_info'),
    path('user/list/', UserListAPIView.as_view(), name='user_list'),
    path('user/assistants/', AssistantListAPIView.as_view(), name='assistant_list'),

    # 系统信息API
    path('system/domain-info/', DomainInfoAPIView.as_view(), name='domain_info'),
]