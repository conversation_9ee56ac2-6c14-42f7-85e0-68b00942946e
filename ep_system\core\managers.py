# core/managers.py
from django.db import models
from django.contrib.auth.models import BaseUserManager

class RegionAwareManager(BaseUserManager):
    """
    一个感知区域的模型管理器。
    它会自动过滤查询集，只返回与当前请求区域相关的数据。
    """
    def get_queryset(self):
        # 获取父类的查询集，例如 User.objects.all() 的结果
        queryset = super().get_queryset()

        # 延迟导入，避免循环导入问题
        from .middleware import get_current_region

        # 获取当前请求的区域对象
        region = get_current_region()

        if region:
            # 如果当前有区域上下文，则自动应用过滤
            return queryset.filter(region=region)

        # 如果没有区域上下文（例如后台任务），则返回所有数据。
        # 这里也可以根据需求抛出错误或返回空。
        return queryset

    def unfiltered(self):
        """
        返回未经区域过滤的查询集
        用于需要绕过区域限制的场景，如登录验证
        """
        return super().get_queryset()

    def get_by_natural_key(self, username):
        """
        通过自然键（用户名）获取用户
        这是Django认证系统需要的方法
        """
        return self.get(**{self.model.USERNAME_FIELD: username})

    def create_user(self, username, email=None, password=None, **extra_fields):
        """
        创建普通用户
        """
        if not username:
            raise ValueError('用户名不能为空')

        email = self.normalize_email(email)
        user = self.model(username=username, email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, username, email=None, password=None, **extra_fields):
        """
        创建超级用户
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('超级用户必须设置is_staff=True')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('超级用户必须设置is_superuser=True')

        return self.create_user(username, email, password, **extra_fields)


class UserAwareManager(models.Manager):
    """
    用户感知的模型管理器
    根据当前登录用户的角色自动过滤数据
    实现精细化的用户级权限控制
    """

    def get_queryset(self):
        """
        根据当前用户角色返回相应的查询集
        """
        queryset = super().get_queryset()

        # 延迟导入，避免循环导入
        from .middleware import get_current_user

        current_user = get_current_user()

        # 如果没有用户上下文（如系统任务），返回所有数据
        if not current_user:
            return queryset

        # 根据用户角色应用不同的过滤策略
        if current_user.role == 'SUPER_ADMIN':
            # 超级管理员：访问所有数据
            return queryset
        elif current_user.role == 'REGION_ADMIN':
            # 区域管理员：访问区域内所有数据（已通过数据库路由实现）
            return queryset
        elif current_user.role == 'MEMBER':
            # 普通会员：只能访问自己创建的数据
            return queryset.filter(created_by_id=current_user.id)
        elif current_user.role == 'MEMBER_ASSISTANT':
            # 会员助理：访问自己创建的数据 + 其负责会员创建的数据
            from .models import CustomUser

            # 获取该助理负责的所有会员ID
            managed_member_ids = list(
                CustomUser.objects.filter(managed_by=current_user)
                .values_list('id', flat=True)
            )

            # 助理可以访问：自己创建的数据 + 其负责会员创建的数据
            accessible_user_ids = [current_user.id] + managed_member_ids
            return queryset.filter(created_by_id__in=accessible_user_ids)
        elif current_user.role == 'WAREHOUSE_MANAGER':
            # 库管：访问仓库相关数据（暂时访问所有区域数据）
            return queryset
        else:
            # 默认：只能访问自己创建的数据
            return queryset.filter(created_by_id=current_user.id)

    def unfiltered(self):
        """
        返回未经用户过滤的查询集
        用于需要绕过用户权限限制的场景
        """
        return super().get_queryset()

    def for_user(self, user):
        """
        返回指定用户可访问的查询集
        用于管理员查看其他用户的数据
        """
        queryset = super().get_queryset()

        if user.role == 'SUPER_ADMIN':
            return queryset
        elif user.role == 'REGION_ADMIN':
            return queryset
        elif user.role == 'MEMBER':
            return queryset.filter(created_by_id=user.id)
        elif user.role == 'MEMBER_ASSISTANT':
            # 会员助理：访问自己创建的数据 + 其负责会员创建的数据
            from .models import CustomUser

            # 获取该助理负责的所有会员ID
            managed_member_ids = list(
                CustomUser.objects.filter(managed_by=user)
                .values_list('id', flat=True)
            )

            # 助理可以访问：自己创建的数据 + 其负责会员创建的数据
            accessible_user_ids = [user.id] + managed_member_ids
            return queryset.filter(created_by_id__in=accessible_user_ids)
        elif user.role == 'WAREHOUSE_MANAGER':
            return queryset
        else:
            return queryset.filter(created_by_id=user.id)

    def create_user(self, username, email=None, password=None, **extra_fields):
        """
        创建普通用户
        """
        if not username:
            raise ValueError('用户名不能为空')

        email = self.normalize_email(email)
        user = self.model(username=username, email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, username, email=None, password=None, **extra_fields):
        """
        创建超级用户
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('超级用户必须设置is_staff=True')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('超级用户必须设置is_superuser=True')

        return self.create_user(username, email, password, **extra_fields)

