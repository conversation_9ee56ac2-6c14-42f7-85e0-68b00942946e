# core/managers.py
from django.db import models

class RegionAwareManager(models.Manager):
    """
    一个感知区域的模型管理器。
    它会自动过滤查询集，只返回与当前请求区域相关的数据。
    """
    def get_queryset(self):
        # 获取父类的查询集，例如 User.objects.all() 的结果
        queryset = super().get_queryset()
        
        # 延迟导入，避免循环导入问题
        from .middleware import get_current_region
        
        # 获取当前请求的区域对象
        region = get_current_region()
        
        if region:
            # 如果当前有区域上下文，则自动应用过滤
            return queryset.filter(region=region)
            
        # 如果没有区域上下文（例如后台任务），则返回所有数据。
        # 这里也可以根据需求抛出错误或返回空。
        return queryset

