# core/managers.py
from django.db import models
from django.contrib.auth.models import BaseUserManager

class RegionAwareManager(BaseUserManager):
    """
    一个感知区域的模型管理器。
    它会自动过滤查询集，只返回与当前请求区域相关的数据。
    """
    def get_queryset(self):
        # 获取父类的查询集，例如 User.objects.all() 的结果
        queryset = super().get_queryset()

        # 延迟导入，避免循环导入问题
        from .middleware import get_current_region

        # 获取当前请求的区域对象
        region = get_current_region()

        if region:
            # 如果当前有区域上下文，则自动应用过滤
            return queryset.filter(region=region)

        # 如果没有区域上下文（例如后台任务），则返回所有数据。
        # 这里也可以根据需求抛出错误或返回空。
        return queryset

    def unfiltered(self):
        """
        返回未经区域过滤的查询集
        用于需要绕过区域限制的场景，如登录验证
        """
        return super().get_queryset()

    def get_by_natural_key(self, username):
        """
        通过自然键（用户名）获取用户
        这是Django认证系统需要的方法
        """
        return self.get(**{self.model.USERNAME_FIELD: username})

    def create_user(self, username, email=None, password=None, **extra_fields):
        """
        创建普通用户
        """
        if not username:
            raise ValueError('用户名不能为空')

        email = self.normalize_email(email)
        user = self.model(username=username, email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, username, email=None, password=None, **extra_fields):
        """
        创建超级用户
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('超级用户必须设置is_staff=True')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('超级用户必须设置is_superuser=True')

        return self.create_user(username, email, password, **extra_fields)

