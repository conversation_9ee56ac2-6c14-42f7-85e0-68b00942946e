#!/usr/bin/env python3
import requests
import json

# 测试权限管理修复后的功能
base_url = 'http://localhost:8000'

def login(username, password):
    """登录获取token"""
    login_data = {
        'userName': username,
        'password': password
    }
    
    response = requests.post(f'{base_url}/api/v1/auth/login/', 
        data=login_data,
        headers={'X-Region-Code': 'MPR'}
    )
    
    if response.status_code == 200:
        return response.json()['data']['token']
    else:
        raise Exception(f"登录失败: {response.text}")

def test_permission_management_fixes(token):
    """测试权限管理修复"""
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR'
    }
    
    print("🔧 测试权限管理修复...")
    
    # 1. 获取用户列表
    print("1️⃣ 获取用户列表...")
    users_response = requests.get(f'{base_url}/api/v1/user/list/', headers=headers)
    if users_response.status_code == 200:
        users = users_response.json()['data']['records']
        print(f"✅ 用户列表获取成功，共 {len(users)} 个用户")
        
        # 选择一个测试用户
        test_user = None
        for user in users:
            if user.get('role') != 'SUPER_ADMIN':
                test_user = user
                break
        
        if not test_user:
            print("❌ 没有找到可测试的用户")
            return False
        
        user_id = test_user['id']
        print(f"📋 选择测试用户: {test_user['userName']} (ID: {user_id})")
        
        # 2. 测试用户详情API
        print("2️⃣ 测试用户详情API...")
        detail_response = requests.get(f'{base_url}/api/v1/user/{user_id}/', headers=headers)
        if detail_response.status_code == 200:
            user_detail = detail_response.json()['data']
            print(f"✅ 用户详情API正常")
            print(f"   用户名: {user_detail['username']}")
            print(f"   角色: {user_detail['roleDisplay']}")
            print(f"   当前权限: {[p['name'] for p in user_detail['permissions']]}")
            
            current_permission_ids = [p['id'] for p in user_detail['permissions']]
            print(f"   当前权限ID: {current_permission_ids}")
        else:
            print(f"❌ 用户详情API失败: {detail_response.text}")
            return False
        
        # 3. 测试权限列表API
        print("3️⃣ 测试权限列表API...")
        perm_response = requests.get(f'{base_url}/api/v1/user/permissions/', headers=headers)
        if perm_response.status_code == 200:
            perm_data = perm_response.json()['data']
            permissions = perm_data['permissions']
            categories = perm_data['categories']
            print(f"✅ 权限列表API正常")
            print(f"   权限总数: {len(permissions)}")
            print(f"   权限分类: {list(categories.keys())}")
            
            # 选择新的权限进行测试
            available_permissions = [p for p in permissions if p['id'] not in current_permission_ids]
            if available_permissions:
                new_permission_ids = current_permission_ids + [available_permissions[0]['id']]
                print(f"   测试权限更新: {len(current_permission_ids)} → {len(new_permission_ids)}")
                
                # 4. 测试权限更新API
                print("4️⃣ 测试权限更新API...")
                update_data = {
                    'username': user_detail['username'],
                    'email': user_detail['email'],
                    'phone': user_detail['phone'],
                    'role': user_detail['role'],
                    'permission_ids': new_permission_ids
                }
                
                # 如果是助理，保持会员关联
                if user_detail['role'] == 'MEMBER_ASSISTANT':
                    update_data['responsible_member_ids'] = [m['id'] for m in user_detail['responsibleMembers']]
                
                update_response = requests.put(f'{base_url}/api/v1/user/{user_id}/', 
                    json=update_data, headers=headers)
                
                if update_response.status_code == 200:
                    print(f"✅ 权限更新API正常")
                    
                    # 验证更新结果
                    verify_response = requests.get(f'{base_url}/api/v1/user/{user_id}/', headers=headers)
                    if verify_response.status_code == 200:
                        updated_detail = verify_response.json()['data']
                        updated_permission_ids = [p['id'] for p in updated_detail['permissions']]
                        print(f"✅ 权限更新验证成功")
                        print(f"   更新后权限ID: {updated_permission_ids}")
                        print(f"   权限数量: {len(updated_permission_ids)}")
                        
                        return len(updated_permission_ids) == len(new_permission_ids)
                    else:
                        print(f"❌ 权限更新验证失败: {verify_response.text}")
                        return False
                else:
                    print(f"❌ 权限更新API失败: {update_response.text}")
                    return False
            else:
                print("⚠️ 用户已拥有所有权限，跳过权限更新测试")
                return True
        else:
            print(f"❌ 权限列表API失败: {perm_response.text}")
            return False
    else:
        print(f"❌ 用户列表获取失败: {users_response.text}")
        return False

def main():
    print("🧪 测试权限管理修复")
    print("=" * 60)
    
    try:
        # 登录
        print("🔐 登录系统...")
        token = login('mpr_admin', 'admin123')
        print("✅ 登录成功!")
        
        # 测试权限管理修复
        print("\n🔄 测试权限管理修复...")
        api_success = test_permission_management_fixes(token)
        
        if api_success:
            print("\n🎉 权限管理修复测试成功！")
            print("\n📋 前端测试步骤:")
            print("1. 打开 http://localhost:3010/#/system/user")
            print("2. 点击任意用户的'权限'按钮（现在应该有图标了）")
            print("3. 查看权限管理对话框是否正常显示")
            print("4. 测试右上角关闭按钮和右下角取消按钮")
            print("5. 测试权限复选框选择功能")
            print("6. 测试全选和清空按钮")
            print("7. 测试保存权限功能")
            
            print("\n🔧 修复内容:")
            print("✅ 权限管理按钮添加了图标")
            print("✅ 修复了对话框关闭按钮问题")
            print("✅ 修复了复选框响应式问题")
            print("✅ 添加了详细的调试信息")
            print("✅ 优化了数据加载和保存逻辑")
        else:
            print("\n❌ 权限管理修复测试失败！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
