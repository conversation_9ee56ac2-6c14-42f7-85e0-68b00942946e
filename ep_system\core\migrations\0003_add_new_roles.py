# Generated manually on 2025-07-08 for adding new user roles

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_alter_customuser_options_alter_customuser_email_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='customuser',
            name='role',
            field=models.CharField(
                choices=[
                    ('SUPER_ADMIN', '超级管理员'),
                    ('REGION_ADMIN', '区域管理员'),
                    ('MEMBER', '普通会员'),
                    ('MEMBER_ASSISTANT', '会员助理'),
                    ('WAREHOUSE_MANAGER', '库管')
                ],
                default='MEMBER',
                max_length=20,
                verbose_name='角色'
            ),
        ),
    ]
