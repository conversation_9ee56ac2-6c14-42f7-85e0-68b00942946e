# business/models.py
"""
业务数据模型
这些模型将存储在区域数据库中，实现数据的物理隔离
"""

from django.db import models
from django.utils import timezone
from core.models import CustomUser


class RegionAwareModel(models.Model):
    """
    区域感知的抽象基类
    所有业务模型都应该继承这个类
    """
    created_at = models.DateTimeField('创建时间', default=timezone.now)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    created_by = models.ForeignKey(
        CustomUser, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='%(class)s_created',
        verbose_name='创建人'
    )
    updated_by = models.ForeignKey(
        CustomUser, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='%(class)s_updated',
        verbose_name='更新人'
    )
    is_active = models.BooleanField('是否激活', default=True)
    
    class Meta:
        abstract = True


class Customer(RegionAwareModel):
    """
    客户信息模型
    存储在区域数据库中
    """
    customer_code = models.CharField('客户编码', max_length=50, unique=True)
    name = models.CharField('客户名称', max_length=200)
    contact_person = models.CharField('联系人', max_length=100, blank=True)
    phone = models.CharField('联系电话', max_length=20, blank=True)
    email = models.EmailField('邮箱', blank=True)
    address = models.TextField('地址', blank=True)
    
    class Meta:
        db_table = 'business_customer'
        verbose_name = '客户'
        verbose_name_plural = '客户'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.customer_code} - {self.name}"


class Product(RegionAwareModel):
    """
    产品信息模型
    存储在区域数据库中
    """
    product_code = models.CharField('产品编码', max_length=50, unique=True)
    name = models.CharField('产品名称', max_length=200)
    description = models.TextField('产品描述', blank=True)
    category = models.CharField('产品类别', max_length=100, blank=True)
    unit = models.CharField('单位', max_length=20, default='个')
    price = models.DecimalField('单价', max_digits=10, decimal_places=2, default=0)
    cost = models.DecimalField('成本', max_digits=10, decimal_places=2, default=0)
    
    class Meta:
        db_table = 'business_product'
        verbose_name = '产品'
        verbose_name_plural = '产品'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.product_code} - {self.name}"


class Order(RegionAwareModel):
    """
    订单信息模型
    存储在区域数据库中
    """
    
    class OrderStatus(models.TextChoices):
        PENDING = 'PENDING', '待处理'
        CONFIRMED = 'CONFIRMED', '已确认'
        PROCESSING = 'PROCESSING', '处理中'
        SHIPPED = 'SHIPPED', '已发货'
        DELIVERED = 'DELIVERED', '已送达'
        CANCELLED = 'CANCELLED', '已取消'
    
    order_number = models.CharField('订单号', max_length=50, unique=True)
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, verbose_name='客户')
    status = models.CharField('订单状态', max_length=20, choices=OrderStatus.choices, default=OrderStatus.PENDING)
    order_date = models.DateTimeField('订单日期', default=timezone.now)
    total_amount = models.DecimalField('订单总额', max_digits=12, decimal_places=2, default=0)
    notes = models.TextField('备注', blank=True)
    
    class Meta:
        db_table = 'business_order'
        verbose_name = '订单'
        verbose_name_plural = '订单'
        ordering = ['-order_date']
    
    def __str__(self):
        return f"{self.order_number} - {self.customer.name}"


class OrderItem(RegionAwareModel):
    """
    订单明细模型
    存储在区域数据库中
    """
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='items', verbose_name='订单')
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name='产品')
    quantity = models.DecimalField('数量', max_digits=10, decimal_places=2)
    unit_price = models.DecimalField('单价', max_digits=10, decimal_places=2)
    total_price = models.DecimalField('小计', max_digits=12, decimal_places=2)
    
    class Meta:
        db_table = 'business_order_item'
        verbose_name = '订单明细'
        verbose_name_plural = '订单明细'
    
    def save(self, *args, **kwargs):
        # 自动计算小计
        self.total_price = self.quantity * self.unit_price
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.order.order_number} - {self.product.name}"


class Inventory(RegionAwareModel):
    """
    库存信息模型
    存储在区域数据库中
    """
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name='产品')
    warehouse_location = models.CharField('仓库位置', max_length=100, blank=True)
    quantity_on_hand = models.DecimalField('现有库存', max_digits=10, decimal_places=2, default=0)
    quantity_reserved = models.DecimalField('预留库存', max_digits=10, decimal_places=2, default=0)
    quantity_available = models.DecimalField('可用库存', max_digits=10, decimal_places=2, default=0)
    reorder_point = models.DecimalField('补货点', max_digits=10, decimal_places=2, default=0)
    last_updated = models.DateTimeField('最后更新', auto_now=True)
    
    class Meta:
        db_table = 'business_inventory'
        verbose_name = '库存'
        verbose_name_plural = '库存'
        unique_together = ['product', 'warehouse_location']
    
    def save(self, *args, **kwargs):
        # 自动计算可用库存
        self.quantity_available = self.quantity_on_hand - self.quantity_reserved
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.product.name} - {self.warehouse_location}"
