#!/usr/bin/env python3
import requests

# 测试简单的域名信息API（不需要认证）
response = requests.get('http://localhost:8000/api/v1/system/domain-info/', 
    headers={'X-Region-Code': 'MPR'}
)

print(f'域名信息API状态码: {response.status_code}')
print(f'响应: {response.text}')

# 测试用户信息API（需要认证，应该返回401）
response2 = requests.get('http://localhost:8000/api/v1/user/info/')
print(f'\n用户信息API状态码: {response2.status_code}')
print(f'响应: {response2.text}')
