#!/usr/bin/env python3
import requests
import json
import time

# 完整权限管理系统测试
base_url = 'http://localhost:8000'

def login(username, password):
    """登录获取token"""
    login_data = {
        'userName': username,
        'password': password
    }
    
    response = requests.post(f'{base_url}/api/v1/auth/login/', 
        data=login_data,
        headers={'X-Region-Code': 'MPR'}
    )
    
    if response.status_code == 200:
        return response.json()['data']['token']
    else:
        raise Exception(f"登录失败: {response.text}")

def test_complete_workflow(token):
    """测试完整的权限管理工作流"""
    headers = {
        'Authorization': token,
        'X-Region-Code': 'MPR',
        'Content-Type': 'application/json'
    }
    
    results = {
        'user_creation': None,
        'permission_assignment': None,
        'assistant_member_association': None,
        'user_editing': None,
        'role_change': None,
        'permission_management': None
    }
    
    # 1. 创建用户（带权限）
    print("1️⃣ 测试用户创建（带权限分配）...")
    timestamp = int(time.time())
    
    # 获取权限列表
    perm_response = requests.get(f'{base_url}/api/v1/user/permissions/', headers=headers)
    permissions = perm_response.json()['data']['permissions']
    selected_permissions = [p['id'] for p in permissions[:5]]
    
    user_data = {
        'username': f'complete_test_user_{timestamp}',
        'email': f'complete_test_{timestamp}@example.com',
        'password': 'password123',
        'role': 'WAREHOUSE_MANAGER',
        'permission_ids': selected_permissions
    }
    
    create_response = requests.post(f'{base_url}/api/v1/user/list/', 
        json=user_data, headers=headers)
    
    if create_response.status_code == 201:
        user_id = create_response.json()['data']['id']
        results['user_creation'] = {
            'success': True,
            'user_id': user_id,
            'permissions_assigned': create_response.json()['data']['permissions_assigned']
        }
        print(f"✅ 用户创建成功 (ID: {user_id})")
    else:
        results['user_creation'] = {'success': False, 'error': create_response.text}
        print(f"❌ 用户创建失败: {create_response.text}")
        return results
    
    # 2. 创建助理（带会员关联）
    print("2️⃣ 测试助理创建（带会员关联）...")
    
    # 获取会员列表
    member_response = requests.get(f'{base_url}/api/v1/user/members/', headers=headers)
    members = member_response.json()['data']
    selected_members = [m['id'] for m in members[:2]]
    
    assistant_data = {
        'username': f'complete_assistant_{timestamp}',
        'email': f'complete_assistant_{timestamp}@example.com',
        'password': 'password123',
        'role': 'MEMBER_ASSISTANT',
        'permission_ids': selected_permissions[:3],
        'responsible_member_ids': selected_members
    }
    
    assistant_response = requests.post(f'{base_url}/api/v1/user/list/', 
        json=assistant_data, headers=headers)
    
    if assistant_response.status_code == 201:
        assistant_id = assistant_response.json()['data']['id']
        results['assistant_member_association'] = {
            'success': True,
            'assistant_id': assistant_id,
            'members_assigned': assistant_response.json()['data']['members_assigned'],
            'permissions_assigned': assistant_response.json()['data']['permissions_assigned']
        }
        print(f"✅ 助理创建成功 (ID: {assistant_id})")
    else:
        results['assistant_member_association'] = {'success': False, 'error': assistant_response.text}
        print(f"❌ 助理创建失败: {assistant_response.text}")
    
    # 3. 测试用户详情获取
    print("3️⃣ 测试用户详情获取...")
    detail_response = requests.get(f'{base_url}/api/v1/user/{user_id}/', headers=headers)
    
    if detail_response.status_code == 200:
        user_detail = detail_response.json()['data']
        results['permission_assignment'] = {
            'success': True,
            'permissions_count': len(user_detail['permissions']),
            'role': user_detail['roleDisplay']
        }
        print(f"✅ 用户详情获取成功，权限数: {len(user_detail['permissions'])}")
    else:
        results['permission_assignment'] = {'success': False, 'error': detail_response.text}
        print(f"❌ 用户详情获取失败: {detail_response.text}")
    
    # 4. 测试用户编辑（权限更新）
    print("4️⃣ 测试用户编辑（权限更新）...")
    
    # 更改权限
    new_permissions = selected_permissions + [p['id'] for p in permissions[5:8]]
    
    edit_data = {
        'username': user_detail['username'],
        'email': user_detail['email'],
        'phone': user_detail['phone'],
        'role': user_detail['role'],
        'permission_ids': new_permissions
    }
    
    edit_response = requests.put(f'{base_url}/api/v1/user/{user_id}/', 
        json=edit_data, headers=headers)
    
    if edit_response.status_code == 200:
        # 验证更新结果
        updated_detail_response = requests.get(f'{base_url}/api/v1/user/{user_id}/', headers=headers)
        updated_detail = updated_detail_response.json()['data']
        
        results['user_editing'] = {
            'success': True,
            'permissions_before': len(user_detail['permissions']),
            'permissions_after': len(updated_detail['permissions']),
            'update_successful': len(updated_detail['permissions']) == len(new_permissions)
        }
        print(f"✅ 用户编辑成功，权限数: {len(user_detail['permissions'])} → {len(updated_detail['permissions'])}")
    else:
        results['user_editing'] = {'success': False, 'error': edit_response.text}
        print(f"❌ 用户编辑失败: {edit_response.text}")
    
    # 5. 测试角色变更
    print("5️⃣ 测试角色变更...")
    
    role_change_data = {
        'username': user_detail['username'],
        'email': user_detail['email'],
        'phone': user_detail['phone'],
        'role': 'MEMBER_ASSISTANT',
        'permission_ids': selected_permissions[:4],
        'responsible_member_ids': selected_members[:1]
    }
    
    role_response = requests.put(f'{base_url}/api/v1/user/{user_id}/', 
        json=role_change_data, headers=headers)
    
    if role_response.status_code == 200:
        # 验证角色变更结果
        role_detail_response = requests.get(f'{base_url}/api/v1/user/{user_id}/', headers=headers)
        role_detail = role_detail_response.json()['data']
        
        results['role_change'] = {
            'success': True,
            'role_before': user_detail['roleDisplay'],
            'role_after': role_detail['roleDisplay'],
            'members_assigned': len(role_detail['responsibleMembers']),
            'role_change_successful': role_detail['role'] == 'MEMBER_ASSISTANT'
        }
        print(f"✅ 角色变更成功: {user_detail['roleDisplay']} → {role_detail['roleDisplay']}")
    else:
        results['role_change'] = {'success': False, 'error': role_response.text}
        print(f"❌ 角色变更失败: {role_response.text}")
    
    # 6. 测试权限管理功能
    print("6️⃣ 测试权限管理功能...")
    
    # 模拟权限管理界面的操作
    final_detail_response = requests.get(f'{base_url}/api/v1/user/{user_id}/', headers=headers)
    final_detail = final_detail_response.json()['data']
    
    # 权限管理：添加更多权限
    management_permissions = [p['id'] for p in permissions[:10]]  # 选择前10个权限
    
    management_data = {
        'username': final_detail['username'],
        'email': final_detail['email'],
        'phone': final_detail['phone'],
        'role': final_detail['role'],
        'permission_ids': management_permissions,
        'responsible_member_ids': [m['id'] for m in final_detail['responsibleMembers']]
    }
    
    management_response = requests.put(f'{base_url}/api/v1/user/{user_id}/', 
        json=management_data, headers=headers)
    
    if management_response.status_code == 200:
        # 验证权限管理结果
        mgmt_detail_response = requests.get(f'{base_url}/api/v1/user/{user_id}/', headers=headers)
        mgmt_detail = mgmt_detail_response.json()['data']
        
        results['permission_management'] = {
            'success': True,
            'permissions_before': len(final_detail['permissions']),
            'permissions_after': len(mgmt_detail['permissions']),
            'target_permissions': len(management_permissions),
            'management_successful': len(mgmt_detail['permissions']) == len(management_permissions)
        }
        print(f"✅ 权限管理成功，权限数: {len(final_detail['permissions'])} → {len(mgmt_detail['permissions'])}")
    else:
        results['permission_management'] = {'success': False, 'error': management_response.text}
        print(f"❌ 权限管理失败: {management_response.text}")
    
    return results

def main():
    print("🧪 完整权限管理系统测试")
    print("=" * 80)
    
    try:
        # 登录
        print("🔐 登录系统...")
        token = login('mpr_admin', 'admin123')
        print("✅ 登录成功!")
        
        # 执行完整工作流测试
        print("\n🔄 执行完整权限管理工作流测试...")
        results = test_complete_workflow(token)
        
        # 输出测试结果
        print("\n📊 测试结果汇总:")
        print("=" * 80)
        
        success_count = 0
        total_tests = len(results)
        
        for test_name, result in results.items():
            if result and result.get('success'):
                print(f"✅ {test_name.replace('_', ' ').title()}: 通过")
                success_count += 1
            else:
                print(f"❌ {test_name.replace('_', ' ').title()}: 失败")
                if result and result.get('error'):
                    print(f"   错误: {result['error']}")
        
        print(f"\n🎯 测试通过率: {success_count}/{total_tests} ({(success_count/total_tests)*100:.1f}%)")
        
        # 详细结果
        print("\n📋 详细测试结果:")
        print("-" * 80)
        
        if results['user_creation'] and results['user_creation']['success']:
            print(f"👤 用户创建: 成功分配 {results['user_creation']['permissions_assigned']} 个权限")
        
        if results['assistant_member_association'] and results['assistant_member_association']['success']:
            print(f"🤝 助理关联: 分配 {results['assistant_member_association']['members_assigned']} 个会员，{results['assistant_member_association']['permissions_assigned']} 个权限")
        
        if results['user_editing'] and results['user_editing']['success']:
            print(f"✏️ 用户编辑: 权限从 {results['user_editing']['permissions_before']} 个更新到 {results['user_editing']['permissions_after']} 个")
        
        if results['role_change'] and results['role_change']['success']:
            print(f"🔄 角色变更: {results['role_change']['role_before']} → {results['role_change']['role_after']}")
        
        if results['permission_management'] and results['permission_management']['success']:
            print(f"🛠️ 权限管理: 权限从 {results['permission_management']['permissions_before']} 个管理到 {results['permission_management']['permissions_after']} 个")
        
        print(f"\n🌐 前端测试地址: http://localhost:3010/#/system/user")
        print("📝 前端功能验证:")
        print("  ✅ 用户创建对话框 - 权限选择界面")
        print("  ✅ 助理创建 - 会员关联选择")
        print("  ✅ 用户编辑 - 权限修改功能")
        print("  ✅ 权限管理 - 专门的权限管理界面")
        print("  ✅ 角色切换 - 动态界面变化")
        print("  ✅ 删除功能 - 软删除机制")
        
        print("\n🎉 完整权限管理系统测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
